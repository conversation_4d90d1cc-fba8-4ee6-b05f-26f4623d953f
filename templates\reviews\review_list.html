{% extends 'base.html' %}

{% block title %}Guest Reviews - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="reviews-container">
    <div class="row">
        <div class="col-12">
            <div class="reviews-header fade-in">
                <h2><i class="fas fa-star pulse-animation"></i> Guest Reviews</h2>
                <p class="text-muted">See what our guests are saying about their stay</p>
                
                <!-- Review Statistics -->
                <div class="review-stats">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number">{{ total_reviews }}</div>
                                <div class="stat-label">Total Reviews</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number">{{ average_rating|floatformat:1 }}</div>
                                <div class="stat-label">Average Rating</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number">{{ recommendation_percentage }}%</div>
                                <div class="stat-label">Would Recommend</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number">{{ recent_reviews_count }}</div>
                                <div class="stat-label">This Month</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Options -->
    <div class="row">
        <div class="col-12">
            <div class="review-filters slide-in">
                <form method="get" class="filter-form">
                    <div class="row">
                        <div class="col-md-3">
                            <select name="rating" class="form-select filter-input">
                                <option value="">All Ratings</option>
                                <option value="5" {% if request.GET.rating == '5' %}selected{% endif %}>5 Stars</option>
                                <option value="4" {% if request.GET.rating == '4' %}selected{% endif %}>4 Stars</option>
                                <option value="3" {% if request.GET.rating == '3' %}selected{% endif %}>3 Stars</option>
                                <option value="2" {% if request.GET.rating == '2' %}selected{% endif %}>2 Stars</option>
                                <option value="1" {% if request.GET.rating == '1' %}selected{% endif %}>1 Star</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="room_type" class="form-select filter-input">
                                <option value="">All Room Types</option>
                                <option value="single" {% if request.GET.room_type == 'single' %}selected{% endif %}>Single</option>
                                <option value="double" {% if request.GET.room_type == 'double' %}selected{% endif %}>Double</option>
                                <option value="suite" {% if request.GET.room_type == 'suite' %}selected{% endif %}>Suite</option>
                                <option value="family" {% if request.GET.room_type == 'family' %}selected{% endif %}>Family</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" name="search" class="form-control filter-input" 
                                   placeholder="Search reviews..." value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100 hover-btn">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reviews List -->
    {% if reviews %}
    <div class="row">
        {% for review in reviews %}
        <div class="col-lg-6 mb-4">
            <div class="review-card fade-in" style="animation-delay: {{ forloop.counter0|add:1|mul:0.1 }}s;">
                <div class="review-header">
                    <div class="reviewer-info">
                        <div class="reviewer-avatar">
                            {% if review.user.profile_picture %}
                                <img src="{{ review.user.profile_picture.url }}" alt="{{ review.user.first_name }}">
                            {% else %}
                                <i class="fas fa-user"></i>
                            {% endif %}
                        </div>
                        <div class="reviewer-details">
                            <h6>{{ review.user.first_name|default:review.user.username }}</h6>
                            <small class="text-muted">{{ review.created_at|date:"F d, Y" }}</small>
                        </div>
                    </div>
                    <div class="review-rating">
                        {% for i in "12345" %}
                            {% if forloop.counter <= review.overall_rating %}
                                <i class="fas fa-star text-warning"></i>
                            {% else %}
                                <i class="far fa-star text-muted"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <div class="review-content">
                    <h5 class="review-title">{{ review.title }}</h5>
                    <p class="review-comment">{{ review.comment }}</p>
                    
                    <div class="review-details">
                        <div class="room-info">
                            <i class="fas fa-bed"></i>
                            <span>{{ review.room.name }} ({{ review.room.get_room_type_display }})</span>
                        </div>
                        
                        {% if review.would_recommend %}
                        <div class="recommendation">
                            <i class="fas fa-thumbs-up text-success"></i>
                            <span>Would recommend</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Detailed Ratings -->
                    <div class="detailed-ratings">
                        <div class="rating-item">
                            <span>Cleanliness</span>
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.cleanliness_rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="rating-item">
                            <span>Service</span>
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.service_rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="rating-item">
                            <span>Location</span>
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.location_rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="rating-item">
                            <span>Value</span>
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.value_rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Reviews pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link hover-btn" href="?page=1{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
            </li>
            <li class="page-item">
                <a class="page-link hover-btn" href="?page={{ page_obj.previous_page_number }}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
            </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
            </li>
            
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link hover-btn" href="?page={{ page_obj.next_page_number }}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
            </li>
            <li class="page-item">
                <a class="page-link hover-btn" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="empty-state">
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-star fa-5x text-muted mb-4"></i>
                        <h4>No reviews yet</h4>
                        <p class="text-muted">Be the first to share your experience with us!</p>
                        <a href="{% url 'bookings:room_list' %}" class="btn btn-primary btn-lg hover-btn">
                            <i class="fas fa-search"></i> Browse Rooms
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.reviews-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.9), rgba(144, 238, 144, 0.1));
    min-height: 80vh;
    padding: 30px;
    border-radius: 20px;
    position: relative;
}

.reviews-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="star-pattern" width="25" height="25" patternUnits="userSpaceOnUse"><rect width="25" height="25" fill="%23F0FFF0" opacity="0.3"/><polygon points="12.5,2 15.5,8.5 22.5,8.5 17,13 19,20 12.5,16 6,20 8,13 2.5,8.5 9.5,8.5" fill="%2332CD32" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23star-pattern)"/></svg>');
    border-radius: 20px;
    z-index: -1;
}

.reviews-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.review-stats {
    margin-top: 30px;
}

.stat-card {
    background: rgba(50, 205, 50, 0.1);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(50, 205, 50, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #32CD32;
    margin-bottom: 5px;
}

.stat-label {
    color: #2d5a2d;
    font-weight: 600;
}

.review-filters {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.review-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    border-left: 5px solid #32CD32;
    height: 100%;
}

.review-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(50, 205, 50, 0.2);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(50, 205, 50, 0.1);
}

.reviewer-info {
    display: flex;
    align-items: center;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #90EE90, #32CD32);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    overflow: hidden;
}

.reviewer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reviewer-avatar i {
    color: white;
    font-size: 1.5rem;
}

.reviewer-details h6 {
    margin: 0;
    color: #2d5a2d;
    font-weight: 600;
}

.review-rating {
    font-size: 1.2rem;
}

.review-title {
    color: #2d5a2d;
    margin-bottom: 15px;
    font-weight: 600;
}

.review-comment {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.review-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(240, 255, 240, 0.5);
    border-radius: 10px;
}

.room-info, .recommendation {
    display: flex;
    align-items: center;
    color: #2d5a2d;
}

.room-info i, .recommendation i {
    margin-right: 8px;
}

.detailed-ratings {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    font-size: 0.9rem;
}

.rating-item span {
    color: #2d5a2d;
    font-weight: 500;
}

.rating-stars {
    font-size: 0.8rem;
}

.empty-state .card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .review-stats .row {
        text-align: center;
    }
    
    .stat-card {
        margin-bottom: 20px;
    }
    
    .review-header {
        flex-direction: column;
        text-align: center;
    }
    
    .reviewer-info {
        margin-bottom: 15px;
    }
    
    .detailed-ratings {
        grid-template-columns: 1fr;
    }
    
    .review-details {
        flex-direction: column;
        text-align: center;
    }
    
    .room-info {
        margin-bottom: 10px;
    }
}
</style>
{% endblock %}
