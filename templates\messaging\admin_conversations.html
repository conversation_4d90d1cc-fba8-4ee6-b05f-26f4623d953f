{% extends 'base.html' %}

{% block title %}Admin - Conversations - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% load static %}{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-comments"></i> Message Management</h1>
                    <p class="admin-subtitle">Manage all client conversations and messages</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'admin_interface:dashboard' %}" class="admin-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{% url 'messaging:send_notification' %}" class="admin-btn btn-primary w-100 mb-2">
                                <i class="fas fa-bell"></i> Send Notification
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'messaging:notifications' %}" class="admin-btn btn-info w-100 mb-2">
                                <i class="fas fa-list"></i> View All Notifications
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="dashboard-card text-center">
                    <h5>Message Stats</h5>
                    <div class="stat-number">{{ conversations|length }}</div>
                    <div class="stat-label">Total Conversations</div>
                </div>
            </div>
        </div>

        <!-- Conversations List -->
        <div class="row">
            <div class="col-12">
                {% if conversations %}
                <div class="dashboard-card">
                    <h5><i class="fas fa-list"></i> Client Conversations</h5>
                    
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Subject</th>
                                    <th>Participants</th>
                                    <th>Last Message</th>
                                    <th>Last Activity</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for conversation in conversations %}
                                <tr>
                                    <td>
                                        <strong>{{ conversation.subject|default:"No Subject" }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            {% for participant in conversation.participants.all %}
                                                <span class="participant-badge 
                                                    {% if participant.user_type == 'admin' %}badge-admin{% else %}badge-client{% endif %}">
                                                    {{ participant.first_name|default:participant.username }}
                                                    <small>({{ participant.get_user_type_display }})</small>
                                                </span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        {% with last_message=conversation.get_last_message %}
                                        {% if last_message %}
                                            <div>
                                                <strong>{{ last_message.sender.first_name|default:last_message.sender.username }}:</strong>
                                                <span class="text-muted">{{ last_message.content|truncatechars:50 }}</span>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">No messages yet</span>
                                        {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ conversation.updated_at|date:"M d, Y" }}</strong>
                                            <small class="text-muted d-block">{{ conversation.updated_at|time:"g:i A" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% with unread_count=conversation.messages.filter:is_read=False %}
                                        {% if unread_count %}
                                            <span class="badge bg-danger">{{ unread_count.count }} unread</span>
                                        {% else %}
                                            <span class="badge bg-success">All read</span>
                                        {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{% url 'messaging:conversation_detail' conversation.pk %}" 
                                               class="admin-btn btn-view btn-sm" title="View Conversation">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'messaging:conversation_detail' conversation.pk %}" 
                                               class="admin-btn btn-primary btn-sm" title="Reply">
                                                <i class="fas fa-reply"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Conversations pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="dashboard-card text-center py-5">
                    <i class="fas fa-comments fa-5x text-muted mb-4"></i>
                    <h4>No conversations yet</h4>
                    <p class="text-muted">No client conversations have been started yet.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Messages Summary -->
        {% if conversations %}
        <div class="row mt-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h5><i class="fas fa-clock"></i> Recent Activity</h5>
                    
                    <div class="recent-messages">
                        {% for conversation in conversations|slice:":5" %}
                        {% with last_message=conversation.get_last_message %}
                        {% if last_message %}
                        <div class="message-summary">
                            <div class="message-avatar">
                                <i class="fas fa-user-circle fa-2x text-primary"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-header">
                                    <strong>{{ last_message.sender.first_name|default:last_message.sender.username }}</strong>
                                    <small class="text-muted">{{ last_message.created_at|timesince }} ago</small>
                                </div>
                                <div class="message-text">
                                    {{ last_message.content|truncatechars:100 }}
                                </div>
                                <div class="message-actions">
                                    <a href="{% url 'messaging:conversation_detail' conversation.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-reply"></i> Reply
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endwith %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.participant-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin: 2px;
    font-weight: 500;
}

.badge-admin {
    background: linear-gradient(135deg, #28A745, #20C997);
    color: white;
}

.badge-client {
    background: linear-gradient(135deg, #007BFF, #17A2B8);
    color: white;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .admin-btn {
    min-width: 35px;
    padding: 6px 10px;
}

.recent-messages {
    max-height: 400px;
    overflow-y: auto;
}

.message-summary {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

.message-summary:hover {
    background-color: rgba(144, 238, 144, 0.05);
}

.message-summary:last-child {
    border-bottom: none;
}

.message-avatar {
    margin-right: 15px;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.message-text {
    color: #666;
    margin-bottom: 10px;
    line-height: 1.4;
}

.message-actions {
    margin-top: 10px;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    border-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(144, 238, 144, 0.1);
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
    
    .action-buttons .admin-btn {
        width: 100%;
        min-width: auto;
    }
    
    .message-summary {
        flex-direction: column;
    }
    
    .message-avatar {
        margin-right: 0;
        margin-bottom: 10px;
        text-align: center;
    }
}
</style>
{% endblock %}
