{% extends 'base.html' %}

{% block title %}Approve Booking - {{ booking.booking_reference }} - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% load static %}{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-check-circle text-success"></i> Approve Booking</h1>
                    <p class="admin-subtitle">Reference: {{ booking.booking_reference }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'admin_interface:booking_details' booking.id %}" class="admin-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Booking
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <div class="text-center mb-4">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h4>Approve Booking Confirmation</h4>
                        <p class="text-muted">You are about to approve this booking request. The guest will be notified immediately.</p>
                    </div>

                    <!-- Booking Summary -->
                    <div class="booking-summary mb-4">
                        <h5><i class="fas fa-info-circle"></i> Booking Summary</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Reference</label>
                                    <div class="info-value">{{ booking.booking_reference }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Guest</label>
                                    <div class="info-value">{{ booking.user.first_name }} {{ booking.user.last_name }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Email</label>
                                    <div class="info-value">{{ booking.user.email }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Phone</label>
                                    <div class="info-value">{{ booking.user.phone_number|default:"Not provided" }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Room</label>
                                    <div class="info-value">{{ booking.room.name }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Check-in</label>
                                    <div class="info-value">{{ booking.check_in_date|date:"F d, Y" }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Check-out</label>
                                    <div class="info-value">{{ booking.check_out_date|date:"F d, Y" }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Total Price</label>
                                    <div class="info-value">
                                        <strong class="text-success">${{ booking.total_price }}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if booking.special_requests %}
                        <div class="info-group mt-3">
                            <label>Special Requests</label>
                            <div class="info-value">
                                <div class="special-requests">
                                    {{ booking.special_requests|linebreaks }}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Room Availability Check -->
                    <div class="availability-check mb-4">
                        <h5><i class="fas fa-calendar-check"></i> Availability Verification</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Room Availability:</strong> 
                            {% if booking.room.is_available_for_dates:booking.check_in_date:booking.check_out_date %}
                                <span class="text-success">✓ Available for selected dates</span>
                            {% else %}
                                <span class="text-warning">⚠ Please verify availability manually</span>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <label>Duration</label>
                                    <div class="stat-value">{{ booking.get_duration_nights }} night{{ booking.get_duration_nights|pluralize }}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <label>Guests</label>
                                    <div class="stat-value">{{ booking.guests_count }} / {{ booking.room.capacity }}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <label>Rate per Night</label>
                                    <div class="stat-value">${{ booking.room.price_per_night }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Approval Form -->
                    <form method="post" class="approval-form">
                        {% csrf_token %}
                        
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <strong>Confirmation:</strong> The guest will be automatically notified about the approval via email and in-app notification.
                        </div>

                        <div class="action-buttons text-center">
                            <button type="submit" class="admin-btn btn-approve me-3"
                                    onclick="return confirm('Are you sure you want to approve this booking?')">
                                <i class="fas fa-check"></i> Approve Booking
                            </button>
                            <a href="{% url 'admin_interface:booking_details' booking.id %}" class="admin-btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.booking-summary {
    background: rgba(144, 238, 144, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid var(--admin-accent);
}

.availability-check {
    background: rgba(23, 162, 184, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #17a2b8;
}

.info-group {
    margin-bottom: 1rem;
}

.info-group label {
    font-weight: 600;
    color: var(--admin-primary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    display: block;
}

.info-value {
    font-size: 1rem;
    color: #333;
}

.special-requests {
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid var(--admin-accent);
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    margin-bottom: 10px;
}

.stat-item label {
    font-weight: 600;
    color: var(--admin-primary);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
    display: block;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.approval-form {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.05));
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: 10px;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(19, 132, 150, 0.05));
    border: 1px solid rgba(23, 162, 184, 0.3);
    border-radius: 10px;
}

@media (max-width: 768px) {
    .action-buttons {
        text-align: center;
    }
    
    .action-buttons .admin-btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .action-buttons .admin-btn:last-child {
        margin-bottom: 0;
    }
    
    .stat-item {
        margin-bottom: 15px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation with booking details
    const approveForm = document.querySelector('.approval-form');
    
    approveForm.addEventListener('submit', function(e) {
        const bookingRef = '{{ booking.booking_reference }}';
        const guestName = '{{ booking.user.first_name }} {{ booking.user.last_name }}';
        const roomName = '{{ booking.room.name }}';
        const checkIn = '{{ booking.check_in_date|date:"M d, Y" }}';
        const checkOut = '{{ booking.check_out_date|date:"M d, Y" }}';
        
        const confirmed = confirm(
            `Approve booking confirmation:\n\n` +
            `Reference: ${bookingRef}\n` +
            `Guest: ${guestName}\n` +
            `Room: ${roomName}\n` +
            `Dates: ${checkIn} - ${checkOut}\n\n` +
            `The guest will be notified immediately. Continue?`
        );
        
        if (!confirmed) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
