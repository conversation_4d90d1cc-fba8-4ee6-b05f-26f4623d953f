{% extends 'base.html' %}
{% load static %}

{% block title %}Admin Dashboard - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-tachometer-alt pulse-animation"></i> Admin Dashboard</h1>
                    <p class="mb-0">Welcome back, {{ user.first_name|default:user.username }}! Here's your system overview.</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="admin-actions">
                        <a href="{% url 'admin:index' %}" class="btn btn-light me-2">
                            <i class="fas fa-cog"></i> Django Admin
                        </a>
                        <a href="{% url 'accounts:logout' %}" class="btn btn-outline-light">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Admin Navigation -->
            <nav class="admin-nav">
                <ul class="nav nav-pills justify-content-center">
                    <li class="nav-item">
                        <a class="nav-link active" href="{% url 'admin_interface:dashboard' %}">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin_interface:bookings' %}">
                            <i class="fas fa-calendar-check"></i> Bookings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin_interface:rooms' %}">
                            <i class="fas fa-bed"></i> Rooms
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin_interface:reviews' %}">
                            <i class="fas fa-star"></i> Reviews
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin_interface:clients' %}">
                            <i class="fas fa-users"></i> Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin_interface:messages' %}">
                            <i class="fas fa-comments"></i> Messages
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card stat-card fade-in-up">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-number">{{ total_bookings }}</div>
                    <div class="stat-label">Total Bookings</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card stat-card fade-in-up" style="animation-delay: 0.1s;">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">{{ pending_bookings }}</div>
                    <div class="stat-label">Pending Bookings</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card stat-card fade-in-up" style="animation-delay: 0.2s;">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-number">${{ monthly_revenue|floatformat:0 }}</div>
                    <div class="stat-label">Monthly Revenue</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card stat-card fade-in-up" style="animation-delay: 0.3s;">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number">{{ total_clients }}</div>
                    <div class="stat-label">Total Clients</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="dashboard-card fade-in-up" style="animation-delay: 0.4s;">
                    <h4><i class="fas fa-bolt"></i> Quick Actions</h4>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <a href="{% url 'admin_interface:pending_bookings' %}" class="admin-btn btn-approve w-100">
                                <i class="fas fa-clock"></i> Pending Bookings ({{ pending_bookings }})
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'admin_interface:pending_reviews' %}" class="admin-btn btn-view w-100">
                                <i class="fas fa-star"></i> Pending Reviews ({{ pending_reviews }})
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'admin_interface:add_room' %}" class="admin-btn btn-edit w-100">
                                <i class="fas fa-plus"></i> Add New Room
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'admin_interface:messages' %}" class="admin-btn btn-decline w-100">
                                <i class="fas fa-envelope"></i> Messages ({{ unread_messages }})
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="dashboard-card slide-in-left">
                    <h5><i class="fas fa-history"></i> Recent Bookings</h5>
                    {% if recent_bookings %}
                        {% for booking in recent_bookings %}
                        <div class="booking-card">
                            <div class="booking-status status-{{ booking.status }}">
                                {{ booking.get_status_display }}
                            </div>
                            <h6>{{ booking.booking_reference }}</h6>
                            <p class="mb-1">
                                <i class="fas fa-user"></i> {{ booking.user.first_name }} {{ booking.user.last_name }}
                            </p>
                            <p class="mb-1">
                                <i class="fas fa-bed"></i> {{ booking.room.name }}
                            </p>
                            <p class="mb-1">
                                <i class="fas fa-calendar"></i> {{ booking.check_in_date }} - {{ booking.check_out_date }}
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> {{ booking.created_at|timesince }} ago
                            </small>
                            <div class="mt-2">
                                <a href="{% url 'admin_interface:booking_details' booking.id %}" class="admin-btn btn-view btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                {% if booking.status == 'pending' %}
                                <a href="{% url 'admin_interface:approve_booking' booking.id %}" class="admin-btn btn-approve btn-sm">
                                    <i class="fas fa-check"></i> Approve
                                </a>
                                <a href="{% url 'admin_interface:decline_booking' booking.id %}" class="admin-btn btn-decline btn-sm">
                                    <i class="fas fa-times"></i> Decline
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No recent bookings</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="dashboard-card slide-in-left" style="animation-delay: 0.2s;">
                    <h5><i class="fas fa-star"></i> Recent Reviews</h5>
                    {% if recent_reviews %}
                        {% for review in recent_reviews %}
                        <div class="booking-card">
                            <div class="booking-status {% if review.is_approved %}status-confirmed{% else %}status-pending{% endif %}">
                                {% if review.is_approved %}Approved{% else %}Pending{% endif %}
                            </div>
                            <h6>{{ review.title }}</h6>
                            <p class="mb-1">
                                <i class="fas fa-user"></i> {{ review.user.first_name }} {{ review.user.last_name }}
                            </p>
                            <p class="mb-1">
                                <i class="fas fa-bed"></i> {{ review.room.name }}
                            </p>
                            <div class="mb-1">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.overall_rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <p class="mb-1">{{ review.comment|truncatewords:15 }}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> {{ review.created_at|timesince }} ago
                            </small>
                            {% if not review.is_approved %}
                            <div class="mt-2">
                                <a href="{% url 'admin_interface:approve_review' review.id %}" class="admin-btn btn-approve btn-sm">
                                    <i class="fas fa-check"></i> Approve
                                </a>
                                <a href="{% url 'admin_interface:reject_review' review.id %}" class="admin-btn btn-decline btn-sm">
                                    <i class="fas fa-times"></i> Reject
                                </a>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No recent reviews</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="row mt-4">
            <div class="col-lg-4">
                <div class="dashboard-card fade-in-up" style="animation-delay: 0.5s;">
                    <h5><i class="fas fa-chart-bar"></i> Room Statistics</h5>
                    <div class="stat-item">
                        <span>Total Rooms:</span>
                        <strong>{{ total_rooms }}</strong>
                    </div>
                    <div class="stat-item">
                        <span>Available Rooms:</span>
                        <strong>{{ available_rooms }}</strong>
                    </div>
                    <div class="stat-item">
                        <span>Occupied Rooms:</span>
                        <strong>{{ occupied_rooms }}</strong>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="dashboard-card fade-in-up" style="animation-delay: 0.6s;">
                    <h5><i class="fas fa-chart-line"></i> Revenue Statistics</h5>
                    <div class="stat-item">
                        <span>Total Revenue:</span>
                        <strong>${{ total_revenue|floatformat:2 }}</strong>
                    </div>
                    <div class="stat-item">
                        <span>Monthly Revenue:</span>
                        <strong>${{ monthly_revenue|floatformat:2 }}</strong>
                    </div>
                    <div class="stat-item">
                        <span>Weekly Bookings:</span>
                        <strong>{{ weekly_bookings }}</strong>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="dashboard-card fade-in-up" style="animation-delay: 0.7s;">
                    <h5><i class="fas fa-users"></i> Client Statistics</h5>
                    <div class="stat-item">
                        <span>Total Clients:</span>
                        <strong>{{ total_clients }}</strong>
                    </div>
                    <div class="stat-item">
                        <span>New This Month:</span>
                        <strong>{{ new_clients }}</strong>
                    </div>
                    <div class="stat-item">
                        <span>Total Reviews:</span>
                        <strong>{{ total_reviews }}</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(44, 85, 48, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add staggered animation delays
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
        location.reload();
    }, 300000);
});
</script>
{% endblock %}
