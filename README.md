# Carthage Hill Guest House Booking System

A comprehensive booking management system for Carthage Hill Guest House built with Django and React Native, featuring a professional web application and mobile app with real-time messaging, booking management, and review system.

## 🌟 Features

### Web Application
- **User Authentication**: Registration, login, logout with role-based access (Client/Admin)
- **Room Management**: Browse, search, and filter available rooms
- **Booking System**: Create, modify, cancel bookings with date validation
- **Admin Dashboard**: Comprehensive admin panel for managing bookings, rooms, and users
- **Real-time Messaging**: WebSocket-based chat system between clients and admin
- **Review System**: Rate and review completed bookings
- **Notification System**: Real-time notifications for booking updates
- **Professional UI**: Light green theme with responsive design
- **API Integration**: RESTful API for mobile app integration
- **Multi-Language Support**: Internationalization with 10+ languages including Arabic, Spanish, French
- **Background Slideshow**: Automatic rotating background images with smooth transitions
- **Enhanced Animations**: Hover effects, transitions, and interactive elements
- **Language Switcher**: Easy language switching in navigation

### Mobile Application (React Native)
- **Cross-platform**: iOS and Android support
- **Native UI**: Professional mobile interface with Material Design
- **Real-time Sync**: Synchronized with web application via API
- **Offline Support**: Basic offline functionality
- **Push Notifications**: Mobile notifications for booking updates
- **Image Upload**: Profile and room image management
- **Date Pickers**: Native date selection for bookings

## 🛠 Technology Stack

### Backend
- **Django 4.2.7**: Web framework
- **Django REST Framework**: API development
- **Django Channels**: WebSocket support for real-time features
- **SQLite**: Database (easily configurable for PostgreSQL/MySQL)
- **Redis**: Channel layer for WebSockets
- **Pillow**: Image processing

### Frontend (Web)
- **Bootstrap 5**: CSS framework
- **Font Awesome**: Icons
- **Custom CSS**: Professional styling with light green theme
- **JavaScript**: Enhanced interactivity

### Mobile App
- **React Native**: Cross-platform mobile development
- **React Navigation**: Navigation system
- **React Native Paper**: Material Design components
- **Axios**: HTTP client for API calls
- **AsyncStorage**: Local data persistence

## 📁 Project Structure

```
carthage-hill-booking-system/
├── carthage_hill_booking/          # Django project settings
├── accounts/                       # User management app
├── bookings/                       # Room and booking management
├── messaging/                      # Chat and notifications
├── reviews/                        # Review system
├── api/                           # REST API endpoints
├── templates/                     # HTML templates
├── static/                        # CSS, JS, images
├── media/                         # User uploaded files
├── mobile_app/                    # React Native mobile app
│   ├── src/
│   │   ├── screens/              # Mobile app screens
│   │   ├── components/           # Reusable components
│   │   ├── services/             # API services
│   │   ├── context/              # React context
│   │   └── theme/                # Styling and theme
│   ├── App.js                    # Main app component
│   └── package.json              # Dependencies
└── requirements.txt               # Python dependencies
```

## 🚀 Installation & Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- Redis Server (for WebSocket support)

### Backend Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd carthage-hill-booking-system
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Configure database**
```bash
python manage.py makemigrations
python manage.py migrate
```

5. **Create superuser**
```bash
python manage.py createsuperuser
```

6. **Load sample data (optional)**
```bash
python manage.py shell
# Run the sample data creation script from the shell
```

7. **Start Redis server**
```bash
redis-server
```

8. **Run development server**
```bash
python manage.py runserver
```

### Mobile App Setup

1. **Navigate to mobile app directory**
```bash
cd mobile_app
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure API endpoint**
Edit `src/services/apiService.js` and update the BASE_URL:
- For Android emulator: `http://********:8000/api`
- For iOS simulator: `http://localhost:8000/api`
- For physical device: `http://YOUR_IP_ADDRESS:8000/api`

4. **Run the app**
```bash
# For Android
npx react-native run-android

# For iOS
npx react-native run-ios
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the project root:
```
DEBUG=True
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///db.sqlite3
REDIS_URL=redis://localhost:6379
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

### Production Settings
For production deployment:
1. Set `DEBUG=False`
2. Configure proper database (PostgreSQL recommended)
3. Set up proper Redis instance
4. Configure email settings
5. Set up static file serving
6. Configure CORS settings for mobile app

## 📱 API Documentation

### Authentication Endpoints
- `POST /api/auth/login/` - User login
- `POST /api/auth/register/` - User registration
- `GET /api/auth/profile/` - Get user profile
- `PATCH /api/auth/profile/` - Update user profile

### Room Endpoints
- `GET /api/rooms/` - List all rooms
- `GET /api/rooms/{id}/` - Get room details
- `GET /api/rooms/{id}/availability/` - Check room availability

### Booking Endpoints
- `GET /api/bookings/` - List user bookings
- `POST /api/bookings/` - Create new booking
- `GET /api/bookings/{id}/` - Get booking details
- `PATCH /api/bookings/{id}/` - Update booking
- `DELETE /api/bookings/{id}/` - Cancel booking

### Messaging Endpoints
- `GET /api/conversations/` - List conversations
- `POST /api/conversations/start/` - Start new conversation
- `GET /api/conversations/{id}/messages/` - Get messages
- `POST /api/conversations/{id}/messages/` - Send message

## 🎨 Design Features

### Color Scheme
- **Primary Green**: #32CD32 (Lime Green)
- **Light Green**: #F0FFF0 (Background)
- **Accent Green**: #90EE90
- **Dark Green**: #228B22 (Text/Buttons)

### UI Components
- Professional card-based layout
- Responsive design for all screen sizes
- Smooth animations and transitions
- Consistent iconography with Font Awesome
- Material Design principles for mobile app

## 👥 User Roles

### Client Users
- Browse and search rooms
- Create, modify, and cancel bookings
- Real-time chat with admin
- Write reviews for completed stays
- Receive notifications about booking status

### Admin Users
- Manage all bookings (approve/reject)
- Add, edit, and manage rooms
- View comprehensive dashboard
- Respond to client messages
- Moderate reviews
- Send system notifications

## 🔒 Security Features

- CSRF protection
- SQL injection prevention
- XSS protection
- Secure password hashing
- Token-based API authentication
- Input validation and sanitization
- File upload security

## 📊 Testing

### Backend Testing
```bash
python manage.py test
```

### API Testing
Use tools like Postman or curl to test API endpoints:
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "testpass"}'
```

## 🚀 Deployment

### Web Application
1. Configure production settings
2. Collect static files: `python manage.py collectstatic`
3. Deploy to platforms like Heroku, DigitalOcean, or AWS
4. Set up proper database and Redis instances
5. Configure domain and SSL certificate

### Mobile Application
1. Build release APK/IPA
2. Test on physical devices
3. Submit to Google Play Store / Apple App Store
4. Configure push notification services

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Phone: +216 XX XXX XXX
- Address: Carthage, Tunisia

## 🙏 Acknowledgments

- Django community for the excellent framework
- React Native team for mobile development tools
- Bootstrap team for the CSS framework
- Font Awesome for the icon library
- All contributors and testers

---

**Carthage Hill Guest House** - Experience comfort and hospitality in the heart of Carthage.
