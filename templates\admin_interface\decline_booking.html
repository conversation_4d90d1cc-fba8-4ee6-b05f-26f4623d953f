{% extends 'base.html' %}

{% block title %}Decline Booking - {{ booking.booking_reference }} - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% load static %}{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-times-circle text-danger"></i> Decline Booking</h1>
                    <p class="admin-subtitle">Reference: {{ booking.booking_reference }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'admin_interface:booking_details' booking.id %}" class="admin-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Booking
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                        <h4>Decline Booking Confirmation</h4>
                        <p class="text-muted">You are about to decline this booking request. This action cannot be undone.</p>
                    </div>

                    <!-- Booking Summary -->
                    <div class="booking-summary mb-4">
                        <h5><i class="fas fa-info-circle"></i> Booking Summary</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Reference</label>
                                    <div class="info-value">{{ booking.booking_reference }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Guest</label>
                                    <div class="info-value">{{ booking.user.first_name }} {{ booking.user.last_name }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Email</label>
                                    <div class="info-value">{{ booking.user.email }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Room</label>
                                    <div class="info-value">{{ booking.room.name }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Check-in</label>
                                    <div class="info-value">{{ booking.check_in_date|date:"F d, Y" }}</div>
                                </div>
                                <div class="info-group">
                                    <label>Check-out</label>
                                    <div class="info-value">{{ booking.check_out_date|date:"F d, Y" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Decline Form -->
                    <form method="post" class="decline-form">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="reason" class="form-label">
                                <i class="fas fa-comment"></i> Reason for Declining <span class="text-danger">*</span>
                            </label>
                            <textarea name="reason" id="reason" class="form-control" rows="4" 
                                      placeholder="Please provide a reason for declining this booking request..." required></textarea>
                            <div class="form-text">
                                This reason will be included in the notification sent to the guest.
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Important:</strong> The guest will be automatically notified about this decision via email and in-app notification.
                        </div>

                        <div class="action-buttons text-center">
                            <button type="submit" class="admin-btn btn-decline me-3"
                                    onclick="return confirm('Are you sure you want to decline this booking? This action cannot be undone.')">
                                <i class="fas fa-times"></i> Decline Booking
                            </button>
                            <a href="{% url 'admin_interface:booking_details' booking.id %}" class="admin-btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.booking-summary {
    background: rgba(144, 238, 144, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid var(--admin-accent);
}

.info-group {
    margin-bottom: 1rem;
}

.info-group label {
    font-weight: 600;
    color: var(--admin-primary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    display: block;
}

.info-value {
    font-size: 1rem;
    color: #333;
}

.decline-form {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.form-control:focus {
    border-color: var(--admin-accent);
    box-shadow: 0 0 0 0.2rem rgba(50, 205, 50, 0.25);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 179, 0, 0.05));
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 10px;
}

@media (max-width: 768px) {
    .action-buttons {
        text-align: center;
    }
    
    .action-buttons .admin-btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .action-buttons .admin-btn:last-child {
        margin-bottom: 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on the reason textarea
    document.getElementById('reason').focus();
    
    // Form validation
    const form = document.querySelector('.decline-form');
    const reasonTextarea = document.getElementById('reason');
    
    form.addEventListener('submit', function(e) {
        const reason = reasonTextarea.value.trim();
        
        if (reason.length < 10) {
            e.preventDefault();
            alert('Please provide a more detailed reason (at least 10 characters).');
            reasonTextarea.focus();
            return false;
        }
        
        // Final confirmation
        const confirmed = confirm(
            'Are you absolutely sure you want to decline this booking?\n\n' +
            'The guest will be notified immediately and this action cannot be undone.'
        );
        
        if (!confirmed) {
            e.preventDefault();
            return false;
        }
    });
    
    // Character counter for reason textarea
    const maxLength = 500;
    const counter = document.createElement('div');
    counter.className = 'form-text text-end';
    counter.style.marginTop = '5px';
    
    function updateCounter() {
        const remaining = maxLength - reasonTextarea.value.length;
        counter.textContent = `${reasonTextarea.value.length}/${maxLength} characters`;
        
        if (remaining < 50) {
            counter.style.color = '#dc3545';
        } else if (remaining < 100) {
            counter.style.color = '#ffc107';
        } else {
            counter.style.color = '#6c757d';
        }
    }
    
    reasonTextarea.setAttribute('maxlength', maxLength);
    reasonTextarea.parentNode.appendChild(counter);
    reasonTextarea.addEventListener('input', updateCounter);
    updateCounter();
});
</script>
{% endblock %}
