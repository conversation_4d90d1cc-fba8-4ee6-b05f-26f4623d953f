{% extends 'base.html' %}

{% block title %}{{ review.title }} - Review - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="review-detail-container">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-star"></i> Review Details</h2>
                        <p class="text-muted">{{ review.title }}</p>
                    </div>
                    <a href="{% url 'reviews:review_list' %}" class="btn btn-outline-secondary hover-btn">
                        <i class="fas fa-arrow-left"></i> Back to Reviews
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Review Content -->
        <div class="col-lg-8">
            <div class="card review-content">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ review.title }}</h5>
                        {% if review.is_approved %}
                            <span class="badge bg-success">Published</span>
                        {% else %}
                            <span class="badge bg-warning text-dark">Pending Approval</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Overall Rating -->
                    <div class="overall-rating-section mb-4">
                        <div class="text-center">
                            <div class="rating-circle">
                                <span class="rating-number">{{ review.overall_rating }}</span>
                                <span class="rating-max">/5</span>
                            </div>
                            <div class="stars mt-2">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.overall_rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <p class="text-muted mt-2">Overall Rating</p>
                        </div>
                    </div>

                    <!-- Detailed Ratings -->
                    <div class="detailed-ratings mb-4">
                        <h6><i class="fas fa-chart-bar"></i> Detailed Ratings</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="rating-bar-item">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span>Cleanliness</span>
                                        <span class="rating-value">{{ review.cleanliness_rating }}/5</span>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: {{ review.cleanliness_rating|floatformat:0|add:0|mul:20 }}%"></div>
                                    </div>
                                </div>
                                
                                <div class="rating-bar-item">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span>Service</span>
                                        <span class="rating-value">{{ review.service_rating }}/5</span>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: {{ review.service_rating|floatformat:0|add:0|mul:20 }}%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="rating-bar-item">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span>Location</span>
                                        <span class="rating-value">{{ review.location_rating }}/5</span>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: {{ review.location_rating|floatformat:0|add:0|mul:20 }}%"></div>
                                    </div>
                                </div>
                                
                                <div class="rating-bar-item">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span>Value for Money</span>
                                        <span class="rating-value">{{ review.value_rating }}/5</span>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: {{ review.value_rating|floatformat:0|add:0|mul:20 }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Review Comment -->
                    <div class="review-comment mb-4">
                        <h6><i class="fas fa-comment"></i> Review</h6>
                        <div class="comment-content">
                            {{ review.comment|linebreaks }}
                        </div>
                    </div>

                    <!-- Recommendation -->
                    {% if review.would_recommend %}
                    <div class="recommendation-section">
                        <div class="alert alert-success">
                            <i class="fas fa-thumbs-up me-2"></i>
                            <strong>{{ review.user.first_name|default:review.user.username }}</strong> would recommend this place to others
                        </div>
                    </div>
                    {% endif %}
                </div>

                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="reviewer-info">
                            <i class="fas fa-user-circle text-primary me-2"></i>
                            <strong>{{ review.user.first_name|default:review.user.username }}</strong>
                            <small class="text-muted ms-2">{{ review.created_at|date:"F d, Y" }}</small>
                        </div>
                        {% if review.user == user %}
                        <div class="review-actions">
                            {% if not review.is_approved %}
                            <a href="{% url 'reviews:edit_review' review.id %}" 
                               class="btn btn-sm btn-outline-primary hover-btn">
                                <i class="fas fa-edit"></i> Edit Review
                            </a>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Room Information -->
            <div class="card room-info mb-4">
                <div class="card-header">
                    <h6><i class="fas fa-bed"></i> Room Information</h6>
                </div>
                <div class="card-body">
                    {% if review.room.room_image %}
                    <div class="room-image mb-3">
                        <img src="{{ review.room.room_image.url }}" alt="{{ review.room.name }}" 
                             class="img-fluid rounded">
                    </div>
                    {% endif %}
                    
                    <div class="room-details">
                        <h6>{{ review.room.name }}</h6>
                        <p class="text-muted mb-2">{{ review.room.get_room_type_display }}</p>
                        <div class="room-features">
                            <div class="feature-item">
                                <i class="fas fa-users text-primary me-2"></i>
                                <span>Up to {{ review.room.capacity }} guests</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-dollar-sign text-success me-2"></i>
                                <span>${{ review.room.price_per_night }}/night</span>
                            </div>
                        </div>
                        
                        {% if review.room.amenities %}
                        <div class="amenities mt-3">
                            <h6>Amenities</h6>
                            <p class="small text-muted">{{ review.room.amenities }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Booking Information -->
            <div class="card booking-info">
                <div class="card-header">
                    <h6><i class="fas fa-calendar-check"></i> Stay Details</h6>
                </div>
                <div class="card-body">
                    <div class="booking-details">
                        <div class="detail-item">
                            <label>Check-in</label>
                            <span>{{ review.booking.check_in_date|date:"F d, Y" }}</span>
                        </div>
                        <div class="detail-item">
                            <label>Check-out</label>
                            <span>{{ review.booking.check_out_date|date:"F d, Y" }}</span>
                        </div>
                        <div class="detail-item">
                            <label>Duration</label>
                            <span>{{ review.booking.get_duration_nights }} night{{ review.booking.get_duration_nights|pluralize }}</span>
                        </div>
                        <div class="detail-item">
                            <label>Guests</label>
                            <span>{{ review.booking.guests_count }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.review-detail-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.8), rgba(144, 238, 144, 0.1));
    min-height: 80vh;
    padding: 20px;
    border-radius: 15px;
}

.page-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.review-content, .room-info, .booking-info {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.review-content .card-header, .room-info .card-header, .booking-info .card-header {
    background: linear-gradient(135deg, #90EE90, #32CD32);
    border-radius: 15px 15px 0 0 !important;
    color: #228B22;
    font-weight: bold;
}

.overall-rating-section {
    background: rgba(144, 238, 144, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #32CD32;
}

.rating-circle {
    display: inline-block;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #32CD32, #90EE90);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin: 0 auto;
}

.rating-number {
    font-size: 2rem;
}

.rating-max {
    font-size: 1rem;
    opacity: 0.8;
}

.stars i {
    font-size: 1.5rem;
    margin: 0 2px;
}

.detailed-ratings {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 10px;
}

.rating-bar-item {
    margin-bottom: 15px;
}

.rating-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.rating-fill {
    height: 100%;
    background: linear-gradient(135deg, #32CD32, #90EE90);
    transition: width 0.3s ease;
}

.rating-value {
    font-weight: 600;
    color: #228B22;
}

.review-comment {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #32CD32;
}

.comment-content {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #333;
}

.room-image img {
    max-height: 200px;
    object-fit: cover;
    width: 100%;
}

.feature-item, .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.feature-item:last-child, .detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 600;
    color: #228B22;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

@media (max-width: 768px) {
    .rating-circle {
        width: 60px;
        height: 60px;
    }
    
    .rating-number {
        font-size: 1.5rem;
    }
    
    .stars i {
        font-size: 1.2rem;
    }
}
</style>
{% endblock %}
