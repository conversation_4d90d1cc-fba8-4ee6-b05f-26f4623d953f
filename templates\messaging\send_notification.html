{% extends 'base.html' %}

{% block title %}Send Notification - Admin - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% load static %}{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-bell"></i> Send Notification</h1>
                    <p class="admin-subtitle">Send notifications to selected users</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'messaging:admin_conversations' %}" class="admin-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Messages
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <h5><i class="fas fa-paper-plane"></i> Compose Notification</h5>
                    
                    <form method="post" class="notification-form">
                        {% csrf_token %}
                        
                        <!-- User Selection -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-users"></i> Select Recipients <span class="text-danger">*</span>
                            </label>
                            <div class="user-selection">
                                <div class="selection-controls mb-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">
                                        <i class="fas fa-check-double"></i> Select All
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="selectNone">
                                        <i class="fas fa-times"></i> Select None
                                    </button>
                                    <span class="selected-count ms-3">
                                        <i class="fas fa-info-circle"></i> 
                                        <span id="countDisplay">0</span> user(s) selected
                                    </span>
                                </div>
                                
                                <div class="users-grid">
                                    {{ form.users }}
                                </div>
                            </div>
                            {% if form.users.help_text %}
                            <div class="form-text">{{ form.users.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Notification Title -->
                        <div class="mb-4">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                <i class="fas fa-heading"></i> Notification Title <span class="text-danger">*</span>
                            </label>
                            {{ form.title }}
                            <div class="form-text">Keep it concise and descriptive</div>
                        </div>

                        <!-- Notification Message -->
                        <div class="mb-4">
                            <label for="{{ form.message.id_for_label }}" class="form-label">
                                <i class="fas fa-comment"></i> Message <span class="text-danger">*</span>
                            </label>
                            {{ form.message }}
                            <div class="form-text">Provide clear and helpful information</div>
                        </div>

                        <!-- Preview Section -->
                        <div class="notification-preview mb-4">
                            <h6><i class="fas fa-eye"></i> Preview</h6>
                            <div class="preview-card">
                                <div class="preview-header">
                                    <i class="fas fa-bell text-primary"></i>
                                    <span class="preview-title">Your notification title will appear here</span>
                                </div>
                                <div class="preview-body">
                                    <p class="preview-message">Your notification message will appear here</p>
                                    <small class="preview-time text-muted">
                                        <i class="fas fa-clock"></i> Just now
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Send Options -->
                        <div class="send-options mb-4">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Note:</strong> Notifications will be sent immediately and users will receive them in their notification panel.
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-actions">
                            <button type="submit" class="admin-btn btn-primary btn-lg" id="sendBtn" disabled>
                                <i class="fas fa-paper-plane"></i> Send Notification
                            </button>
                            <a href="{% url 'messaging:admin_conversations' %}" class="admin-btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.notification-form {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.user-selection {
    background: rgba(144, 238, 144, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid var(--admin-accent);
}

.selection-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.selected-count {
    font-weight: 600;
    color: var(--admin-primary);
}

.users-grid {
    max-height: 300px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.users-grid ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
}

.users-grid li {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

.users-grid li:hover {
    background: rgba(144, 238, 144, 0.2);
}

.users-grid input[type="checkbox"] {
    margin-right: 8px;
}

.users-grid label {
    display: flex;
    align-items: center;
    margin: 0;
    cursor: pointer;
    font-weight: 500;
}

.notification-preview {
    background: rgba(23, 162, 184, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #17a2b8;
}

.preview-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.preview-header {
    background: linear-gradient(135deg, #007BFF, #17A2B8);
    color: white;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-title {
    font-weight: 600;
    font-size: 0.95rem;
}

.preview-body {
    padding: 15px;
}

.preview-message {
    margin-bottom: 10px;
    color: #333;
    line-height: 1.5;
}

.preview-time {
    font-size: 0.85rem;
}

.form-actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.form-actions .admin-btn {
    margin: 0 10px;
}

@media (max-width: 768px) {
    .selection-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .users-grid ul {
        grid-template-columns: 1fr;
    }
    
    .form-actions .admin-btn {
        width: 100%;
        margin: 5px 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllBtn = document.getElementById('selectAll');
    const selectNoneBtn = document.getElementById('selectNone');
    const countDisplay = document.getElementById('countDisplay');
    const sendBtn = document.getElementById('sendBtn');
    const titleInput = document.getElementById('id_title');
    const messageInput = document.getElementById('id_message');
    const previewTitle = document.querySelector('.preview-title');
    const previewMessage = document.querySelector('.preview-message');
    
    // Get all user checkboxes
    const userCheckboxes = document.querySelectorAll('input[name="users"]');
    
    // Update selected count
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll('input[name="users"]:checked').length;
        countDisplay.textContent = selectedCount;
        
        // Enable/disable send button
        const hasTitle = titleInput.value.trim().length > 0;
        const hasMessage = messageInput.value.trim().length > 0;
        const hasUsers = selectedCount > 0;
        
        sendBtn.disabled = !(hasTitle && hasMessage && hasUsers);
    }
    
    // Select all users
    selectAllBtn.addEventListener('click', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        updateSelectedCount();
    });
    
    // Select no users
    selectNoneBtn.addEventListener('click', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        updateSelectedCount();
    });
    
    // Listen for checkbox changes
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    // Update preview
    function updatePreview() {
        const title = titleInput.value.trim() || 'Your notification title will appear here';
        const message = messageInput.value.trim() || 'Your notification message will appear here';
        
        previewTitle.textContent = title;
        previewMessage.textContent = message;
        
        updateSelectedCount();
    }
    
    // Listen for input changes
    titleInput.addEventListener('input', updatePreview);
    messageInput.addEventListener('input', updatePreview);
    
    // Initial update
    updateSelectedCount();
    
    // Form validation
    const form = document.querySelector('.notification-form');
    form.addEventListener('submit', function(e) {
        const selectedUsers = document.querySelectorAll('input[name="users"]:checked').length;
        const title = titleInput.value.trim();
        const message = messageInput.value.trim();
        
        if (selectedUsers === 0) {
            e.preventDefault();
            alert('Please select at least one user to send the notification to.');
            return;
        }
        
        if (!title) {
            e.preventDefault();
            alert('Please provide a notification title.');
            titleInput.focus();
            return;
        }
        
        if (!message) {
            e.preventDefault();
            alert('Please provide a notification message.');
            messageInput.focus();
            return;
        }
        
        // Confirmation
        const confirmed = confirm(
            `Send notification to ${selectedUsers} user(s)?\n\n` +
            `Title: ${title}\n` +
            `Message: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`
        );
        
        if (!confirmed) {
            e.preventDefault();
            return;
        }
    });
});
</script>
{% endblock %}
