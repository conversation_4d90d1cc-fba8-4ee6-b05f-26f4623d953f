/* Enhanced Admin Interface Styles */

:root {
    --admin-primary: #2C5530;
    --admin-secondary: #4A7C59;
    --admin-accent: #32CD32;
    --admin-light: #90EE90;
    --admin-bg: #F0FFF0;
    --admin-white: #FFFFFF;
    --admin-shadow: rgba(44, 85, 48, 0.15);
    --admin-shadow-hover: rgba(44, 85, 48, 0.25);
    --admin-gradient: linear-gradient(135deg, #2C5530, #4A7C59);
    --admin-gradient-light: linear-gradient(135deg, #90EE90, #32CD32);
}

/* Admin Layout */
.admin-container {
    background: linear-gradient(135deg, var(--admin-bg) 0%, #FFFFFF 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.admin-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(50, 205, 50, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(144, 238, 144, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(44, 85, 48, 0.05) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
}

/* Admin Header */
.admin-header {
    background: var(--admin-gradient);
    color: white;
    padding: 20px 0;
    box-shadow: 0 4px 20px var(--admin-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.admin-header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.admin-nav {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    margin-top: 15px;
}

.admin-nav .nav-link {
    color: white !important;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    margin: 0 5px;
    font-weight: 500;
}

.admin-nav .nav-link:hover,
.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--admin-white);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px var(--admin-shadow);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--admin-gradient-light);
}

.dashboard-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px var(--admin-shadow-hover);
}

.stat-card {
    text-align: center;
    position: relative;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--admin-primary);
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    animation: countUp 1s ease-out;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--admin-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-icon {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 4rem;
    color: var(--admin-light);
    opacity: 0.3;
    z-index: 0;
}

/* Booking Cards */
.booking-card {
    background: var(--admin-white);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px var(--admin-shadow);
    transition: all 0.3s ease;
    border-left: 5px solid var(--admin-accent);
    position: relative;
    overflow: hidden;
}

.booking-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.booking-card:hover::before {
    left: 100%;
}

.booking-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px var(--admin-shadow-hover);
}

.booking-status {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-pending {
    background: linear-gradient(135deg, #FFA500, #FFD700);
    color: white;
    animation: pulse 2s infinite;
}

.status-confirmed {
    background: linear-gradient(135deg, #32CD32, #90EE90);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, #DC3545, #FF6B6B);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, #6C757D, #ADB5BD);
    color: white;
}

/* Action Buttons */
.admin-btn {
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
    margin: 5px;
}

.admin-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.admin-btn:hover::before {
    width: 300px;
    height: 300px;
}

.admin-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-approve {
    background: linear-gradient(135deg, #28A745, #20C997);
    color: white;
}

.btn-decline {
    background: linear-gradient(135deg, #DC3545, #E74C3C);
    color: white;
}

.btn-view {
    background: linear-gradient(135deg, #007BFF, #17A2B8);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #FFC107, #FFB300);
    color: white;
}

/* Tables */
.admin-table {
    background: var(--admin-white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px var(--admin-shadow);
    margin-bottom: 30px;
}

.admin-table thead {
    background: var(--admin-gradient);
    color: white;
}

.admin-table th {
    padding: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
}

.admin-table td {
    padding: 15px 20px;
    border: none;
    border-bottom: 1px solid rgba(44, 85, 48, 0.1);
    vertical-align: middle;
}

.admin-table tbody tr {
    transition: all 0.3s ease;
}

.admin-table tbody tr:hover {
    background: rgba(144, 238, 144, 0.1);
    transform: scale(1.01);
}

/* Filters and Search */
.admin-filters {
    background: var(--admin-white);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px var(--admin-shadow);
}

.filter-group {
    margin-bottom: 20px;
}

.filter-group label {
    font-weight: 600;
    color: var(--admin-primary);
    margin-bottom: 8px;
}

.filter-input {
    border: 2px solid var(--admin-light);
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    width: 100%;
}

.filter-input:focus {
    border-color: var(--admin-accent);
    box-shadow: 0 0 15px rgba(50, 205, 50, 0.3);
    transform: scale(1.02);
}

/* Animations */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Animation Classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header h1 {
        font-size: 1.5rem;
    }
    
    .dashboard-card {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .booking-card {
        padding: 15px;
    }
    
    .admin-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 10px;
        font-size: 0.9rem;
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

/* Success/Error Messages */
.admin-alert {
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    animation: slideInLeft 0.5s ease-out;
}

.admin-alert-success {
    background: linear-gradient(135deg, #D4EDDA, #C3E6CB);
    color: #155724;
    border-left: 5px solid #28A745;
}

.admin-alert-error {
    background: linear-gradient(135deg, #F8D7DA, #F5C6CB);
    color: #721C24;
    border-left: 5px solid #DC3545;
}

.admin-alert-warning {
    background: linear-gradient(135deg, #FFF3CD, #FFEAA7);
    color: #856404;
    border-left: 5px solid #FFC107;
}

.admin-alert-info {
    background: linear-gradient(135deg, #CCE7FF, #B8DAFF);
    color: #004085;
    border-left: 5px solid #007BFF;
}
