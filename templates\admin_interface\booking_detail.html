{% extends 'admin_base.html' %}

{% block title %}Booking Details - {{ booking.booking_reference }} - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% load static %}{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-calendar-check"></i> Booking Details</h1>
                    <p class="admin-subtitle">Reference: {{ booking.booking_reference }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'admin_interface:bookings' %}" class="admin-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Bookings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row">
            <!-- Booking Information -->
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <h5><i class="fas fa-info-circle"></i> Booking Information</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label>Booking Reference</label>
                                <div class="info-value">
                                    <strong class="text-primary">{{ booking.booking_reference }}</strong>
                                </div>
                            </div>
                            
                            <div class="info-group">
                                <label>Status</label>
                                <div class="info-value">
                                    <span class="status-badge status-{{ booking.status }}">
                                        {{ booking.get_status_display }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="info-group">
                                <label>Check-in Date</label>
                                <div class="info-value">{{ booking.check_in_date|date:"F d, Y" }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Check-out Date</label>
                                <div class="info-value">{{ booking.check_out_date|date:"F d, Y" }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Duration</label>
                                <div class="info-value">{{ booking.get_duration_nights }} night{{ booking.get_duration_nights|pluralize }}</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-group">
                                <label>Number of Guests</label>
                                <div class="info-value">{{ booking.guests_count }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Total Price</label>
                                <div class="info-value">
                                    <strong class="text-success">${{ booking.total_price }}</strong>
                                </div>
                            </div>
                            
                            <div class="info-group">
                                <label>Created</label>
                                <div class="info-value">{{ booking.created_at|date:"F d, Y g:i A" }}</div>
                            </div>
                            
                            {% if booking.confirmed_by %}
                            <div class="info-group">
                                <label>Confirmed By</label>
                                <div class="info-value">{{ booking.confirmed_by.first_name }} {{ booking.confirmed_by.last_name }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Confirmed At</label>
                                <div class="info-value">{{ booking.confirmed_at|date:"F d, Y g:i A" }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if booking.special_requests %}
                    <div class="info-group mt-3">
                        <label>Special Requests</label>
                        <div class="info-value">
                            <div class="special-requests">
                                {{ booking.special_requests|linebreaks }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Guest Information -->
                <div class="dashboard-card mt-4">
                    <h5><i class="fas fa-user"></i> Guest Information</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label>Name</label>
                                <div class="info-value">{{ booking.user.first_name }} {{ booking.user.last_name }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Email</label>
                                <div class="info-value">
                                    <a href="mailto:{{ booking.user.email }}">{{ booking.user.email }}</a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-group">
                                <label>Phone Number</label>
                                <div class="info-value">
                                    {% if booking.user.phone_number %}
                                        <a href="tel:{{ booking.user.phone_number }}">{{ booking.user.phone_number }}</a>
                                    {% else %}
                                        <span class="text-muted">Not provided</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="info-group">
                                <label>Member Since</label>
                                <div class="info-value">{{ booking.user.date_joined|date:"F Y" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Room Information -->
                <div class="dashboard-card mt-4">
                    <h5><i class="fas fa-bed"></i> Room Information</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label>Room Name</label>
                                <div class="info-value">{{ booking.room.name }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Room Type</label>
                                <div class="info-value">{{ booking.room.get_room_type_display }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Capacity</label>
                                <div class="info-value">{{ booking.room.capacity }} guest{{ booking.room.capacity|pluralize }}</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-group">
                                <label>Price per Night</label>
                                <div class="info-value">${{ booking.room.price_per_night }}</div>
                            </div>
                            
                            <div class="info-group">
                                <label>Amenities</label>
                                <div class="info-value">{{ booking.room.amenities }}</div>
                            </div>
                        </div>
                    </div>
                    
                    {% if booking.room.room_image %}
                    <div class="info-group mt-3">
                        <label>Room Image</label>
                        <div class="info-value">
                            <img src="{{ booking.room.room_image.url }}" alt="{{ booking.room.name }}" 
                                 class="room-image img-fluid rounded">
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Actions Panel -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <h5><i class="fas fa-cogs"></i> Actions</h5>
                    
                    {% if booking.status == 'pending' %}
                    <div class="d-grid gap-2">
                        <form method="post" action="{% url 'admin_interface:approve_booking' booking.id %}">
                            {% csrf_token %}
                            <button type="submit" class="admin-btn btn-approve w-100"
                                    onclick="return confirm('Are you sure you want to approve this booking?')">
                                <i class="fas fa-check"></i> Approve Booking
                            </button>
                        </form>
                        
                        <form method="post" action="{% url 'admin_interface:decline_booking' booking.id %}">
                            {% csrf_token %}
                            <button type="submit" class="admin-btn btn-decline w-100"
                                    onclick="return confirm('Are you sure you want to decline this booking?')">
                                <i class="fas fa-times"></i> Decline Booking
                            </button>
                        </form>
                    </div>
                    {% endif %}
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'admin_interface:client_details' booking.user.id %}" 
                           class="admin-btn btn-info">
                            <i class="fas fa-user"></i> View Guest Profile
                        </a>
                        
                        <a href="mailto:{{ booking.user.email }}?subject=Regarding your booking {{ booking.booking_reference }}" 
                           class="admin-btn btn-secondary">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                        
                        {% if booking.user.phone_number %}
                        <a href="tel:{{ booking.user.phone_number }}" 
                           class="admin-btn btn-secondary">
                            <i class="fas fa-phone"></i> Call Guest
                        </a>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Booking Timeline -->
                <div class="dashboard-card mt-4">
                    <h5><i class="fas fa-history"></i> Timeline</h5>
                    
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>Booking Created</h6>
                                <small class="text-muted">{{ booking.created_at|date:"M d, Y g:i A" }}</small>
                            </div>
                        </div>
                        
                        {% if booking.confirmed_at %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6>Booking Confirmed</h6>
                                <small class="text-muted">{{ booking.confirmed_at|date:"M d, Y g:i A" }}</small>
                                <small class="text-muted d-block">by {{ booking.confirmed_by.first_name }} {{ booking.confirmed_by.last_name }}</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    margin-bottom: 1rem;
}

.info-group label {
    font-weight: 600;
    color: var(--admin-primary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    display: block;
}

.info-value {
    font-size: 1rem;
    color: #333;
}

.special-requests {
    background: rgba(144, 238, 144, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid var(--admin-accent);
}

.room-image {
    max-height: 200px;
    object-fit: cover;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--admin-light);
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: linear-gradient(135deg, #FFC107, #FFB300);
    color: #000;
}

.status-confirmed {
    background: linear-gradient(135deg, #28A745, #20C997);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #DC3545, #E74C3C);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, #6C757D, #495057);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, #17A2B8, #138496);
    color: white;
}
</style>
{% endblock %}
