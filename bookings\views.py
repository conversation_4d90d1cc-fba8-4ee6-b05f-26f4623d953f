from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy
from django.db.models import Q
from django.utils import timezone
from .models import Room, Booking
from .forms import BookingForm, RoomSearchForm, RoomForm
from messaging.models import Notification

class RoomListView(ListView):
    model = Room
    template_name = 'bookings/room_list.html'
    context_object_name = 'rooms'
    paginate_by = 12

    def get_queryset(self):
        queryset = Room.objects.filter(is_available=True)
        form = RoomSearchForm(self.request.GET)

        if form.is_valid():
            check_in_date = form.cleaned_data.get('check_in_date')
            check_out_date = form.cleaned_data.get('check_out_date')
            guests_count = form.cleaned_data.get('guests_count')
            room_type = form.cleaned_data.get('room_type')
            max_price = form.cleaned_data.get('max_price')

            if check_in_date and check_out_date:
                # Filter rooms available for the date range
                available_room_ids = []
                for room in queryset:
                    if room.is_available_for_dates(check_in_date, check_out_date):
                        available_room_ids.append(room.id)
                queryset = queryset.filter(id__in=available_room_ids)

            if guests_count:
                queryset = queryset.filter(capacity__gte=guests_count)

            if room_type:
                queryset = queryset.filter(room_type=room_type)

            if max_price:
                queryset = queryset.filter(price_per_night__lte=max_price)

        return queryset.order_by('price_per_night')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = RoomSearchForm(self.request.GET)
        return context

class RoomDetailView(DetailView):
    model = Room
    template_name = 'bookings/room_detail.html'
    context_object_name = 'room'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated:
            context['booking_form'] = BookingForm(user=self.request.user, initial={'room': self.object})
        context['reviews'] = self.object.reviews.filter(is_approved=True).order_by('-created_at')[:5]
        return context

@login_required
def create_booking(request, room_id):
    room = get_object_or_404(Room, id=room_id, is_available=True)

    if request.method == 'POST':
        form = BookingForm(request.POST, user=request.user)
        if form.is_valid():
            booking = form.save(commit=False)
            booking.user = request.user
            booking.save()

            # Create notification for all admin users
            from django.contrib.auth import get_user_model
            User = get_user_model()
            admin_users = User.objects.filter(user_type='admin')

            for admin_user in admin_users:
                Notification.objects.create(
                    user=admin_user,
                    notification_type='booking_created',
                    title='New Booking Request',
                    message=f'New booking request from {request.user.username} for {room.name}',
                    related_booking=booking
                )

            messages.success(request, f'Booking request submitted successfully! Reference: {booking.booking_reference}')
            return redirect('bookings:booking_detail', booking.id)
    else:
        form = BookingForm(user=request.user, initial={'room': room})

    return render(request, 'bookings/create_booking.html', {
        'form': form,
        'room': room
    })

class BookingDetailView(LoginRequiredMixin, DetailView):
    model = Booking
    template_name = 'bookings/booking_detail.html'
    context_object_name = 'booking'

    def get_queryset(self):
        if self.request.user.is_admin:
            return Booking.objects.all()
        return Booking.objects.filter(user=self.request.user)

class MyBookingsView(LoginRequiredMixin, ListView):
    model = Booking
    template_name = 'bookings/my_bookings.html'
    context_object_name = 'bookings'
    paginate_by = 10

    def get_queryset(self):
        return Booking.objects.filter(user=self.request.user).order_by('-created_at')

@login_required
def cancel_booking(request, booking_id):
    booking = get_object_or_404(Booking, id=booking_id, user=request.user)

    if not booking.can_be_cancelled():
        messages.error(request, 'This booking cannot be cancelled.')
        return redirect('bookings:booking_detail', booking.id)

    if request.method == 'POST':
        booking.status = 'cancelled'
        booking.save()

        # Create notification for admin
        Notification.objects.create(
            user_id=1,  # Admin user
            notification_type='booking_cancelled',
            title='Booking Cancelled',
            message=f'Booking {booking.booking_reference} has been cancelled by {request.user.username}',
            related_booking=booking
        )

        messages.success(request, 'Booking cancelled successfully.')
        return redirect('bookings:my_bookings')

    return render(request, 'bookings/cancel_booking.html', {'booking': booking})

@login_required
def modify_booking(request, booking_id):
    booking = get_object_or_404(Booking, id=booking_id, user=request.user)

    if not booking.can_be_modified():
        messages.error(request, 'This booking cannot be modified.')
        return redirect('bookings:booking_detail', booking.id)

    if request.method == 'POST':
        form = BookingForm(request.POST, instance=booking, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Booking modified successfully.')
            return redirect('bookings:booking_detail', booking.id)
    else:
        form = BookingForm(instance=booking, user=request.user)

    return render(request, 'bookings/modify_booking.html', {
        'form': form,
        'booking': booking
    })

# Admin Views
class AdminBookingsView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Booking
    template_name = 'bookings/admin_bookings.html'
    context_object_name = 'bookings'
    paginate_by = 20

    def test_func(self):
        return self.request.user.is_admin

    def get_queryset(self):
        queryset = Booking.objects.all().order_by('-created_at')
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_filter'] = self.request.GET.get('status', '')
        return context

@login_required
def approve_booking(request, booking_id):
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    booking = get_object_or_404(Booking, id=booking_id)

    if request.method == 'POST':
        booking.status = 'confirmed'
        booking.confirmed_by = request.user
        booking.confirmed_at = timezone.now()
        booking.save()

        # Create notification for user
        Notification.objects.create(
            user=booking.user,
            notification_type='booking_confirmed',
            title='Booking Confirmed',
            message=f'Your booking {booking.booking_reference} has been confirmed!',
            related_booking=booking
        )

        messages.success(request, f'Booking {booking.booking_reference} approved successfully.')
        return redirect('bookings:admin_bookings')

    return render(request, 'bookings/approve_booking.html', {'booking': booking})

@login_required
def reject_booking(request, booking_id):
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    booking = get_object_or_404(Booking, id=booking_id)

    if request.method == 'POST':
        booking.status = 'rejected'
        booking.save()

        # Create notification for user
        Notification.objects.create(
            user=booking.user,
            notification_type='booking_rejected',
            title='Booking Rejected',
            message=f'Your booking {booking.booking_reference} has been rejected.',
            related_booking=booking
        )

        messages.success(request, f'Booking {booking.booking_reference} rejected.')
        return redirect('bookings:admin_bookings')

    return render(request, 'bookings/reject_booking.html', {'booking': booking})

class AdminRoomsView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Room
    template_name = 'bookings/admin_rooms.html'
    context_object_name = 'rooms'
    paginate_by = 20

    def test_func(self):
        return self.request.user.is_admin

    def get_queryset(self):
        return Room.objects.all().order_by('name')

class RoomCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = Room
    form_class = RoomForm
    template_name = 'bookings/room_form.html'
    success_url = reverse_lazy('bookings:admin_rooms')

    def test_func(self):
        return self.request.user.is_admin

    def form_valid(self, form):
        messages.success(self.request, 'Room created successfully!')
        return super().form_valid(form)

class RoomUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Room
    form_class = RoomForm
    template_name = 'bookings/room_form.html'
    success_url = reverse_lazy('bookings:admin_rooms')

    def test_func(self):
        return self.request.user.is_admin

    def form_valid(self, form):
        messages.success(self.request, 'Room updated successfully!')
        return super().form_valid(form)

@login_required
def availability_calendar(request):
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    rooms = Room.objects.all()
    bookings = Booking.objects.filter(
        status__in=['confirmed', 'pending'],
        check_out_date__gte=timezone.now().date()
    ).order_by('check_in_date')

    return render(request, 'bookings/availability_calendar.html', {
        'rooms': rooms,
        'bookings': bookings
    })
