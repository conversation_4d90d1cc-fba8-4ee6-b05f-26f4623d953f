{% extends 'base.html' %}

{% block title %}Write a Review - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="review-container">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h2><i class="fas fa-star"></i> Write a Review</h2>
                <p class="text-muted">Share your experience with other guests</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Booking Summary -->
        <div class="col-lg-4">
            <div class="card booking-summary">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Your Stay</h5>
                </div>
                <div class="card-body">
                    <div class="booking-info">
                        <div class="info-item">
                            <label>Room</label>
                            <div class="info-value">{{ booking.room.name }}</div>
                        </div>
                        <div class="info-item">
                            <label>Check-in</label>
                            <div class="info-value">{{ booking.check_in_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="info-item">
                            <label>Check-out</label>
                            <div class="info-value">{{ booking.check_out_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="info-item">
                            <label>Duration</label>
                            <div class="info-value">{{ booking.get_duration_nights }} night{{ booking.get_duration_nights|pluralize }}</div>
                        </div>
                        <div class="info-item">
                            <label>Guests</label>
                            <div class="info-value">{{ booking.guests_count }}</div>
                        </div>
                    </div>
                    
                    {% if booking.room.room_image %}
                    <div class="room-image mt-3">
                        <img src="{{ booking.room.room_image.url }}" alt="{{ booking.room.name }}" 
                             class="img-fluid rounded">
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Review Form -->
        <div class="col-lg-8">
            <div class="card review-form-card">
                <div class="card-header">
                    <h5><i class="fas fa-edit"></i> Your Review</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="review-form">
                        {% csrf_token %}
                        
                        <!-- Title and Comment -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="{{ form.title.id_for_label }}" class="form-label">
                                    <i class="fas fa-heading"></i> Review Title
                                </label>
                                {{ form.title }}
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="{{ form.comment.id_for_label }}" class="form-label">
                                    <i class="fas fa-comment"></i> Your Experience
                                </label>
                                {{ form.comment }}
                                <div class="form-text">Share details about your stay to help other guests</div>
                            </div>
                        </div>

                        <!-- Ratings -->
                        <div class="ratings-section mb-4">
                            <h6><i class="fas fa-star"></i> Rate Your Experience</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.overall_rating.id_for_label }}" class="form-label">
                                        Overall Rating
                                    </label>
                                    <div class="rating-input">
                                        {{ form.overall_rating }}
                                        <div class="star-display" data-rating="overall_rating"></div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.cleanliness_rating.id_for_label }}" class="form-label">
                                        Cleanliness
                                    </label>
                                    <div class="rating-input">
                                        {{ form.cleanliness_rating }}
                                        <div class="star-display" data-rating="cleanliness_rating"></div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.service_rating.id_for_label }}" class="form-label">
                                        Service
                                    </label>
                                    <div class="rating-input">
                                        {{ form.service_rating }}
                                        <div class="star-display" data-rating="service_rating"></div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.location_rating.id_for_label }}" class="form-label">
                                        Location
                                    </label>
                                    <div class="rating-input">
                                        {{ form.location_rating }}
                                        <div class="star-display" data-rating="location_rating"></div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.value_rating.id_for_label }}" class="form-label">
                                        Value for Money
                                    </label>
                                    <div class="rating-input">
                                        {{ form.value_rating }}
                                        <div class="star-display" data-rating="value_rating"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recommendation -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    {{ form.would_recommend }}
                                    <label class="form-check-label" for="{{ form.would_recommend.id_for_label }}">
                                        <i class="fas fa-thumbs-up"></i> I would recommend this place to others
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-success btn-lg hover-btn">
                                <i class="fas fa-paper-plane"></i> Submit Review
                            </button>
                            <a href="{% url 'bookings:booking_detail' booking.id %}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.review-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.8), rgba(144, 238, 144, 0.1));
    min-height: 80vh;
    padding: 20px;
    border-radius: 15px;
}

.page-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.booking-summary {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.booking-summary .card-header {
    background: linear-gradient(135deg, #90EE90, #32CD32);
    border-radius: 15px 15px 0 0 !important;
    color: #228B22;
    font-weight: bold;
}

.info-item {
    margin-bottom: 1rem;
}

.info-item label {
    font-weight: 600;
    color: #228B22;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    display: block;
}

.info-value {
    font-size: 1rem;
    color: #333;
}

.review-form-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.review-form-card .card-header {
    background: linear-gradient(135deg, #90EE90, #32CD32);
    border-radius: 15px 15px 0 0 !important;
    color: #228B22;
    font-weight: bold;
}

.ratings-section {
    background: rgba(144, 238, 144, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #32CD32;
}

.rating-input {
    position: relative;
}

.rating-input select {
    margin-bottom: 5px;
}

.star-display {
    display: flex;
    gap: 2px;
}

.star-display .star {
    color: #ddd;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.star-display .star.filled {
    color: #ffc107;
}

.form-actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.form-actions .btn {
    margin: 0 10px;
}

.room-image img {
    max-height: 150px;
    object-fit: cover;
    width: 100%;
}

@media (max-width: 768px) {
    .form-actions .btn {
        width: 100%;
        margin: 5px 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize star displays
    function updateStarDisplay(selectElement, starContainer) {
        const rating = parseInt(selectElement.value) || 0;
        starContainer.innerHTML = '';
        
        for (let i = 1; i <= 5; i++) {
            const star = document.createElement('span');
            star.className = `star fas fa-star ${i <= rating ? 'filled' : ''}`;
            starContainer.appendChild(star);
        }
    }
    
    // Set up star displays for all rating fields
    document.querySelectorAll('.star-display').forEach(starContainer => {
        const ratingField = starContainer.getAttribute('data-rating');
        const selectElement = document.getElementById(`id_${ratingField}`);
        
        if (selectElement) {
            // Initial display
            updateStarDisplay(selectElement, starContainer);
            
            // Update on change
            selectElement.addEventListener('change', function() {
                updateStarDisplay(this, starContainer);
            });
        }
    });
    
    // Form validation
    const form = document.querySelector('.review-form');
    form.addEventListener('submit', function(e) {
        const title = document.getElementById('id_title').value.trim();
        const comment = document.getElementById('id_comment').value.trim();
        const overallRating = document.getElementById('id_overall_rating').value;
        
        if (!title) {
            e.preventDefault();
            alert('Please provide a title for your review.');
            document.getElementById('id_title').focus();
            return;
        }
        
        if (!comment || comment.length < 10) {
            e.preventDefault();
            alert('Please provide a detailed comment (at least 10 characters).');
            document.getElementById('id_comment').focus();
            return;
        }
        
        if (!overallRating) {
            e.preventDefault();
            alert('Please provide an overall rating.');
            document.getElementById('id_overall_rating').focus();
            return;
        }
    });
});
</script>
{% endblock %}
