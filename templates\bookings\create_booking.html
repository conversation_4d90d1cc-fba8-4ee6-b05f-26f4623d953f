{% extends 'base.html' %}

{% block title %}Book {{ room.name }} - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="booking-container">
    <div class="row">
        <div class="col-lg-8">
            <div class="booking-form-card fade-in">
                <div class="card-header">
                    <h3><i class="fas fa-calendar-plus"></i> Book Your Stay</h3>
                    <p class="text-muted">Complete your reservation for {{ room.name }}</p>
                </div>
                <div class="card-body">
                    <form method="post" class="booking-form">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.check_in_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Check-in Date
                                    </label>
                                    {{ form.check_in_date }}
                                    {% if form.check_in_date.errors %}
                                        <div class="text-danger">{{ form.check_in_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.check_out_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Check-out Date
                                    </label>
                                    {{ form.check_out_date }}
                                    {% if form.check_out_date.errors %}
                                        <div class="text-danger">{{ form.check_out_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.guests_count.id_for_label }}" class="form-label">
                                        <i class="fas fa-users"></i> Number of Guests
                                    </label>
                                    {{ form.guests_count }}
                                    {% if form.guests_count.errors %}
                                        <div class="text-danger">{{ form.guests_count.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="price-display">
                                    <label class="form-label">
                                        <i class="fas fa-dollar-sign"></i> Price per Night
                                    </label>
                                    <div class="price-amount">${{ room.price_per_night }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.special_requests.id_for_label }}" class="form-label">
                                <i class="fas fa-comment"></i> Special Requests (Optional)
                            </label>
                            {{ form.special_requests }}
                            {% if form.special_requests.errors %}
                                <div class="text-danger">{{ form.special_requests.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="booking-summary">
                            <h5><i class="fas fa-receipt"></i> Booking Summary</h5>
                            <div class="summary-item">
                                <span>Room:</span>
                                <span>{{ room.name }}</span>
                            </div>
                            <div class="summary-item">
                                <span>Room Type:</span>
                                <span>{{ room.get_room_type_display }}</span>
                            </div>
                            <div class="summary-item">
                                <span>Price per Night:</span>
                                <span>${{ room.price_per_night }}</span>
                            </div>
                            <div class="summary-total">
                                <span>Total Amount:</span>
                                <span id="total-amount">$0.00</span>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success btn-lg hover-btn pulse-animation">
                                <i class="fas fa-check-circle"></i> Confirm Booking
                            </button>
                            <a href="{% url 'bookings:room_detail' room.id %}" class="btn btn-outline-secondary btn-lg hover-btn">
                                <i class="fas fa-arrow-left"></i> Back to Room
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="room-info-card slide-in">
                <div class="card-header">
                    <h5><i class="fas fa-bed"></i> Room Information</h5>
                </div>
                <div class="card-body">
                    {% if room.room_image %}
                    <img src="{{ room.room_image.url }}" class="room-image" alt="{{ room.name }}">
                    {% else %}
                    <div class="room-placeholder">
                        <i class="fas fa-bed fa-3x"></i>
                    </div>
                    {% endif %}
                    
                    <h6 class="room-title">{{ room.name }}</h6>
                    <p class="room-description">{{ room.description|truncatewords:20 }}</p>
                    
                    <div class="room-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Up to {{ room.capacity }} guests</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-tag"></i>
                            <span>{{ room.get_room_type_display }}</span>
                        </div>
                        {% if room.amenities %}
                        <div class="feature-item">
                            <i class="fas fa-star"></i>
                            <span>Premium amenities included</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="booking-policies">
                <h6><i class="fas fa-info-circle"></i> Booking Policies</h6>
                <ul>
                    <li>Check-in: 3:00 PM</li>
                    <li>Check-out: 11:00 AM</li>
                    <li>Cancellation: Free up to 24 hours before</li>
                    <li>Payment: Secure online payment</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.booking-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.9), rgba(144, 238, 144, 0.1));
    min-height: 80vh;
    padding: 30px;
    border-radius: 20px;
    position: relative;
}

.booking-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hotel-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><rect width="20" height="20" fill="%23F0FFF0" opacity="0.5"/><circle cx="10" cy="10" r="2" fill="%2332CD32" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23hotel-pattern)"/></svg>');
    border-radius: 20px;
    z-index: -1;
}

.booking-form-card, .room-info-card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(50, 205, 50, 0.2);
    margin-bottom: 20px;
    overflow: hidden;
}

.booking-form-card .card-header, .room-info-card .card-header {
    background: linear-gradient(135deg, #32CD32, #90EE90);
    color: white;
    padding: 25px;
    border: none;
}

.form-group label {
    font-weight: 600;
    color: #2d5a2d;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #90EE90;
    border-radius: 15px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #32CD32;
    box-shadow: 0 0 15px rgba(50, 205, 50, 0.3);
    transform: scale(1.02);
}

.price-display {
    text-align: center;
    padding: 15px;
    background: rgba(50, 205, 50, 0.1);
    border-radius: 15px;
    border: 2px solid #90EE90;
}

.price-amount {
    font-size: 2rem;
    font-weight: bold;
    color: #32CD32;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.booking-summary {
    background: rgba(240, 255, 240, 0.8);
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
    border-left: 5px solid #32CD32;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(50, 205, 50, 0.2);
}

.summary-total {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    font-weight: bold;
    font-size: 1.2rem;
    color: #2d5a2d;
    border-top: 2px solid #32CD32;
    margin-top: 10px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.room-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 15px;
}

.room-placeholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #90EE90, #32CD32);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    margin-bottom: 15px;
    color: white;
}

.room-title {
    color: #2d5a2d;
    font-weight: bold;
    margin-bottom: 10px;
}

.room-features {
    margin-top: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #2d5a2d;
}

.feature-item i {
    margin-right: 10px;
    color: #32CD32;
    width: 20px;
}

.booking-policies {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.booking-policies h6 {
    color: #2d5a2d;
    margin-bottom: 15px;
}

.booking-policies ul {
    list-style: none;
    padding: 0;
}

.booking-policies li {
    padding: 5px 0;
    color: #666;
    border-bottom: 1px solid rgba(50, 205, 50, 0.1);
}

.booking-policies li:before {
    content: '✓';
    color: #32CD32;
    margin-right: 10px;
    font-weight: bold;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkInInput = document.querySelector('input[name="check_in_date"]');
    const checkOutInput = document.querySelector('input[name="check_out_date"]');
    const totalAmountElement = document.getElementById('total-amount');
    const pricePerNight = {{ room.price_per_night }};

    function calculateTotal() {
        if (checkInInput.value && checkOutInput.value) {
            const checkIn = new Date(checkInInput.value);
            const checkOut = new Date(checkOutInput.value);
            const timeDiff = checkOut.getTime() - checkIn.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
            
            if (daysDiff > 0) {
                const total = daysDiff * pricePerNight;
                totalAmountElement.textContent = `$${total.toFixed(2)}`;
                totalAmountElement.style.color = '#32CD32';
            } else {
                totalAmountElement.textContent = '$0.00';
                totalAmountElement.style.color = '#dc3545';
            }
        }
    }

    checkInInput.addEventListener('change', calculateTotal);
    checkOutInput.addEventListener('change', calculateTotal);
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    checkInInput.setAttribute('min', today);
    checkOutInput.setAttribute('min', today);
});
</script>
{% endblock %}
