// Carthage Hill Guest House - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeBackgroundSlideshow();
    initializeNotifications();
    initializeChat();
    initializeFormValidation();
    initializeImagePreview();
    initializeStarRating();
    initializeTooltips();
    initializeSearchFilters();
    initializeEnhancedAnimations();
});

// Background Slideshow
function initializeBackgroundSlideshow() {
    const slides = document.querySelectorAll('.background-slide');
    if (slides.length === 0) return;

    let currentSlide = 0;

    function showNextSlide() {
        slides[currentSlide].classList.remove('active');
        currentSlide = (currentSlide + 1) % slides.length;
        slides[currentSlide].classList.add('active');
    }

    // Change slide every 8 seconds
    setInterval(showNextSlide, 8000);
}

// Enhanced Animations
function initializeEnhancedAnimations() {
    // Fade in animations
    const fadeElements = document.querySelectorAll('.fade-in, .fade-in-up, .slide-in-left, .slide-in-right');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0) translateX(0)';
            }
        });
    }, observerOptions);

    fadeElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

        if (element.classList.contains('fade-in-up')) {
            element.style.transform = 'translateY(30px)';
        } else if (element.classList.contains('slide-in-left')) {
            element.style.transform = 'translateX(-30px)';
        } else if (element.classList.contains('slide-in-right')) {
            element.style.transform = 'translateX(30px)';
        }

        observer.observe(element);
    });

    // Hover effects for cards
    const hoverCards = document.querySelectorAll('.hover-card');
    hoverCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
            this.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.2)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
        });
    });

    // Pulse animation for important elements
    const pulseElements = document.querySelectorAll('.pulse-animation');
    pulseElements.forEach(element => {
        element.style.animation = 'pulse 2s infinite';
    });
}

// Notification System
function initializeNotifications() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.classList.contains('alert-dismissible')) {
                const closeBtn = alert.querySelector('.btn-close');
                if (closeBtn) closeBtn.click();
            }
        }, 5000);
    });

    // Mark notification as read when clicked
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            if (notificationId) {
                markNotificationAsRead(notificationId);
            }
        });
    });
}

function markNotificationAsRead(notificationId) {
    fetch(`/messaging/notification/${notificationId}/read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotificationBadge();
        }
    })
    .catch(error => console.error('Error:', error));
}

function updateNotificationBadge() {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        const count = parseInt(badge.textContent) - 1;
        if (count <= 0) {
            badge.style.display = 'none';
        } else {
            badge.textContent = count;
        }
    }
}

// Real-time Chat System
function initializeChat() {
    const chatContainer = document.querySelector('.chat-container');
    if (!chatContainer) return;

    const conversationId = chatContainer.dataset.conversationId;
    if (!conversationId) return;

    // WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/chat/${conversationId}/`;
    
    const chatSocket = new WebSocket(wsUrl);

    chatSocket.onmessage = function(e) {
        const data = JSON.parse(e.data);
        if (data.type === 'chat_message') {
            appendMessage(data.message);
        }
    };

    chatSocket.onclose = function(e) {
        console.error('Chat socket closed unexpectedly');
    };

    // Send message form
    const messageForm = document.querySelector('#message-form');
    if (messageForm) {
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const messageInput = document.querySelector('#id_content');
            const message = messageInput.value.trim();
            
            if (message) {
                chatSocket.send(JSON.stringify({
                    'message': message
                }));
                messageInput.value = '';
            }
        });
    }
}

function appendMessage(message) {
    const chatContainer = document.querySelector('.chat-container');
    const currentUserId = parseInt(document.body.dataset.userId);
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${message.sender_id === currentUserId ? 'sent' : 'received'}`;
    messageDiv.innerHTML = `
        <div class="message-content">${message.content}</div>
        <div class="message-time">${formatTime(message.created_at)}</div>
    `;
    
    chatContainer.appendChild(messageDiv);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// Form Validation Enhancement
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="loading"></span> Processing...';
                
                // Re-enable after 3 seconds to prevent permanent disable
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = submitBtn.dataset.originalText || 'Submit';
                }, 3000);
            }
        });
    });

    // Date validation for booking forms
    const checkInInput = document.querySelector('#id_check_in_date');
    const checkOutInput = document.querySelector('#id_check_out_date');
    
    if (checkInInput && checkOutInput) {
        checkInInput.addEventListener('change', validateDates);
        checkOutInput.addEventListener('change', validateDates);
    }
}

function validateDates() {
    const checkInInput = document.querySelector('#id_check_in_date');
    const checkOutInput = document.querySelector('#id_check_out_date');
    
    if (checkInInput.value && checkOutInput.value) {
        const checkIn = new Date(checkInInput.value);
        const checkOut = new Date(checkOutInput.value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (checkIn < today) {
            showAlert('Check-in date cannot be in the past', 'danger');
            checkInInput.value = '';
            return;
        }
        
        if (checkOut <= checkIn) {
            showAlert('Check-out date must be after check-in date', 'danger');
            checkOutInput.value = '';
            return;
        }
    }
}

// Image Preview
function initializeImagePreview() {
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    imageInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    let preview = document.querySelector(`#${input.id}-preview`);
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.id = `${input.id}-preview`;
                        preview.className = 'img-thumbnail mt-2';
                        preview.style.maxWidth = '200px';
                        input.parentNode.appendChild(preview);
                    }
                    preview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    });
}

// Star Rating System
function initializeStarRating() {
    const ratingSelects = document.querySelectorAll('select[id*="rating"]');
    ratingSelects.forEach(select => {
        createStarRating(select);
    });
}

function createStarRating(select) {
    const container = document.createElement('div');
    container.className = 'star-rating-container';
    
    const stars = document.createElement('div');
    stars.className = 'star-rating';
    
    for (let i = 1; i <= 5; i++) {
        const star = document.createElement('span');
        star.innerHTML = '★';
        star.dataset.rating = i;
        star.addEventListener('click', function() {
            select.value = i;
            updateStars(stars, i);
        });
        stars.appendChild(star);
    }
    
    select.style.display = 'none';
    select.parentNode.insertBefore(container, select);
    container.appendChild(stars);
    container.appendChild(select);
    
    // Initialize with current value
    updateStars(stars, select.value || 0);
}

function updateStars(container, rating) {
    const stars = container.querySelectorAll('span');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.remove('empty');
        } else {
            star.classList.add('empty');
        }
    });
}

// Initialize Bootstrap Tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Search Filters
function initializeSearchFilters() {
    const searchForm = document.querySelector('#search-form');
    if (searchForm) {
        const inputs = searchForm.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // Auto-submit form when filters change
                setTimeout(() => {
                    searchForm.submit();
                }, 500);
            });
        });
    }
}

// Utility Functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

// Price Calculator for Booking
function calculateTotalPrice() {
    const checkInInput = document.querySelector('#id_check_in_date');
    const checkOutInput = document.querySelector('#id_check_out_date');
    const pricePerNight = parseFloat(document.querySelector('#price-per-night')?.dataset.price || 0);
    
    if (checkInInput?.value && checkOutInput?.value && pricePerNight) {
        const checkIn = new Date(checkInInput.value);
        const checkOut = new Date(checkOutInput.value);
        const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
        
        if (nights > 0) {
            const total = nights * pricePerNight;
            const totalElement = document.querySelector('#total-price');
            if (totalElement) {
                totalElement.textContent = `$${total.toFixed(2)} (${nights} night${nights !== 1 ? 's' : ''})`;
            }
        }
    }
}

// Initialize price calculator if on booking page
if (document.querySelector('#id_check_in_date')) {
    document.querySelector('#id_check_in_date').addEventListener('change', calculateTotalPrice);
    document.querySelector('#id_check_out_date').addEventListener('change', calculateTotalPrice);
}
