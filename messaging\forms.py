from django import forms
from django.contrib.auth import get_user_model
from .models import Message, Notification

User = get_user_model()

class MessageForm(forms.ModelForm):
    class Meta:
        model = Message
        fields = ['content', 'attachment']
        widgets = {
            'content': forms.Textarea(attrs={
                'rows': 3,
                'placeholder': 'Type your message here...',
                'class': 'form-control'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['content'].widget.attrs.update({'class': 'form-control'})

class NotificationForm(forms.Form):
    users = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(user_type='client'),
        widget=forms.CheckboxSelectMultiple,
        required=True,
        help_text="Select users to send notification to"
    )
    title = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'placeholder': 'Notification title'})
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'placeholder': 'Notification message'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields.values():
            if field.widget.__class__.__name__ != 'CheckboxSelectMultiple':
                field.widget.attrs.update({'class': 'form-control'})
