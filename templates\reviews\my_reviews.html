{% extends 'base.html' %}

{% block title %}My Reviews - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="reviews-container">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h2><i class="fas fa-star"></i> My Reviews</h2>
                <p class="text-muted">Manage your reviews and feedback</p>
            </div>
        </div>
    </div>

    {% if reviews %}
    <div class="row">
        {% for review in reviews %}
        <div class="col-lg-6 mb-4">
            <div class="card review-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ review.title }}</h6>
                    <div class="review-status">
                        {% if review.is_approved %}
                            <span class="badge bg-success">Published</span>
                        {% else %}
                            <span class="badge bg-warning text-dark">Pending Approval</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Room and Booking Info -->
                    <div class="booking-info mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-bed text-primary me-2"></i>
                            <strong>{{ review.room.name }}</strong>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            <small class="text-muted">
                                {{ review.booking.check_in_date|date:"M d" }} - {{ review.booking.check_out_date|date:"M d, Y" }}
                            </small>
                        </div>
                    </div>

                    <!-- Overall Rating -->
                    <div class="overall-rating mb-3">
                        <div class="d-flex align-items-center">
                            <span class="rating-label me-2">Overall:</span>
                            <div class="stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.overall_rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="rating-number ms-2">{{ review.overall_rating }}/5</span>
                        </div>
                    </div>

                    <!-- Detailed Ratings -->
                    <div class="detailed-ratings mb-3">
                        <div class="row">
                            <div class="col-6">
                                <div class="rating-item">
                                    <small class="text-muted">Cleanliness:</small>
                                    <div class="mini-stars">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.cleanliness_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="rating-item">
                                    <small class="text-muted">Service:</small>
                                    <div class="mini-stars">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.service_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="rating-item">
                                    <small class="text-muted">Location:</small>
                                    <div class="mini-stars">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.location_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="rating-item">
                                    <small class="text-muted">Value:</small>
                                    <div class="mini-stars">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.value_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Review Comment -->
                    <div class="review-comment">
                        <p class="mb-2">{{ review.comment|truncatewords:20 }}</p>
                        {% if review.would_recommend %}
                            <div class="recommendation">
                                <i class="fas fa-thumbs-up text-success me-1"></i>
                                <small class="text-success">Would recommend</small>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> {{ review.created_at|date:"M d, Y" }}
                        </small>
                        <div class="review-actions">
                            <a href="{% url 'reviews:review_detail' review.id %}" 
                               class="btn btn-sm btn-outline-primary hover-btn">
                                <i class="fas fa-eye"></i> View
                            </a>
                            {% if not review.is_approved %}
                            <a href="{% url 'reviews:edit_review' review.id %}" 
                               class="btn btn-sm btn-outline-secondary hover-btn">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Reviews pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">First</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="empty-state">
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-star fa-5x text-muted mb-4"></i>
                        <h4>No reviews yet</h4>
                        <p class="text-muted">You haven't written any reviews yet. Complete a booking to leave your first review!</p>
                        <a href="{% url 'bookings:room_list' %}" class="btn btn-primary btn-lg hover-btn">
                            <i class="fas fa-search"></i> Browse Rooms
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.reviews-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.8), rgba(144, 238, 144, 0.1));
    min-height: 70vh;
    padding: 20px;
    border-radius: 15px;
}

.page-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.review-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.review-card .card-header {
    background: linear-gradient(135deg, #90EE90, #32CD32);
    border-radius: 15px 15px 0 0 !important;
    color: #228B22;
    font-weight: bold;
}

.booking-info {
    background: rgba(144, 238, 144, 0.1);
    padding: 10px;
    border-radius: 8px;
    border-left: 3px solid #32CD32;
}

.overall-rating {
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 8px;
}

.rating-label {
    font-weight: 600;
    color: #333;
}

.rating-number {
    font-weight: 600;
    color: #228B22;
}

.detailed-ratings {
    background: rgba(255, 255, 255, 0.5);
    padding: 10px;
    border-radius: 8px;
}

.rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.mini-stars {
    font-size: 0.8rem;
}

.mini-stars i {
    margin-right: 1px;
}

.stars i {
    margin-right: 2px;
    font-size: 1.1rem;
}

.review-comment {
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #32CD32;
}

.recommendation {
    margin-top: 10px;
}

.review-actions .btn {
    margin-left: 5px;
}

.empty-state .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

@media (max-width: 768px) {
    .review-actions {
        margin-top: 10px;
    }
    
    .review-actions .btn {
        width: 100%;
        margin: 2px 0;
    }
    
    .detailed-ratings .row {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}
