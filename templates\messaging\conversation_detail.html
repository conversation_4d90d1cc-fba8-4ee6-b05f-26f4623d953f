{% extends 'base.html' %}

{% block title %}{{ conversation.subject }} - Messages{% endblock %}

{% block content %}
<div class="conversation-container">
    <div class="row">
        <div class="col-12">
            <div class="conversation-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3><i class="fas fa-comments"></i> {{ conversation.subject }}</h3>
                        <p class="text-muted mb-0">
                            Conversation with 
                            {% for participant in conversation.participants.all %}
                                {% if participant != user %}
                                    {{ participant.first_name|default:participant.username }}
                                {% endif %}
                            {% endfor %}
                        </p>
                    </div>
                    <a href="{% url 'messaging:conversations' %}" class="btn btn-outline-secondary hover-btn">
                        <i class="fas fa-arrow-left"></i> Back to Messages
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="messages-container" id="messages-container">
                {% for message in messages %}
                <div class="message-item {% if message.sender == user %}sent{% else %}received{% endif %} fade-in">
                    <div class="message-content">
                        <div class="message-header">
                            <strong>{{ message.sender.first_name|default:message.sender.username }}</strong>
                            <small class="text-muted">{{ message.created_at|date:"M d, Y g:i A" }}</small>
                        </div>
                        <div class="message-body">
                            {{ message.content|linebreaks }}
                        </div>
                        {% if message.attachment %}
                        <div class="message-attachment">
                            <a href="{{ message.attachment.url }}" target="_blank" class="btn btn-sm btn-outline-primary hover-btn">
                                <i class="fas fa-paperclip"></i> View Attachment
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="no-messages">
                    <p class="text-center text-muted">No messages yet. Start the conversation!</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="message-form-container">
                <form method="post" action="{% url 'messaging:send_message' conversation.id %}" enctype="multipart/form-data" class="message-form">
                    {% csrf_token %}
                    <div class="input-group">
                        <textarea name="content" class="form-control message-input" placeholder="Type your message..." rows="3" required></textarea>
                        <div class="input-group-append">
                            <button type="submit" class="btn btn-success send-btn hover-btn">
                                <i class="fas fa-paper-plane"></i> Send
                            </button>
                        </div>
                    </div>
                    <div class="form-group mt-2">
                        <input type="file" name="attachment" class="form-control-file" id="attachment">
                        <small class="form-text text-muted">Optional: Attach a file (max 10MB)</small>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.conversation-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.8), rgba(144, 238, 144, 0.1));
    min-height: 80vh;
    padding: 20px;
    border-radius: 15px;
}

.conversation-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.messages-container {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.message-item {
    margin-bottom: 20px;
    animation: fadeIn 0.5s ease-in;
}

.message-item.sent {
    text-align: right;
}

.message-item.sent .message-content {
    background: linear-gradient(135deg, #32CD32, #90EE90);
    color: white;
    margin-left: 20%;
    border-radius: 20px 20px 5px 20px;
}

.message-item.received .message-content {
    background: rgba(248, 249, 250, 0.9);
    color: #333;
    margin-right: 20%;
    border-radius: 20px 20px 20px 5px;
    border-left: 4px solid #32CD32;
}

.message-content {
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.message-content:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(50, 205, 50, 0.2);
}

.message-header {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.message-body {
    line-height: 1.5;
}

.message-attachment {
    margin-top: 10px;
}

.message-form-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.message-input {
    border-radius: 25px;
    border: 2px solid #90EE90;
    resize: none;
    transition: all 0.3s ease;
}

.message-input:focus {
    border-color: #32CD32;
    box-shadow: 0 0 15px rgba(50, 205, 50, 0.3);
}

.send-btn {
    border-radius: 25px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #32CD32, #90EE90);
    border: none;
}

.hover-btn {
    transition: all 0.3s ease;
}

.hover-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(50, 205, 50, 0.4);
}

.no-messages {
    text-align: center;
    padding: 40px;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
    width: 8px;
}

.messages-container::-webkit-scrollbar-track {
    background: rgba(240, 255, 240, 0.5);
    border-radius: 10px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #90EE90;
    border-radius: 10px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #32CD32;
}
</style>

<script>
// Auto-scroll to bottom of messages
document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('messages-container');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // Auto-resize textarea
    const messageInput = document.querySelector('.message-input');
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
{% endblock %}
