{% extends 'admin_base.html' %}

{% block title %}Client Details - {{ client.first_name }} {{ client.last_name }} - Admin - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% load static %}{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-user"></i> Client Details</h1>
                    <p class="admin-subtitle">{{ client.first_name }} {{ client.last_name }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'admin_interface:clients' %}" class="admin-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Clients
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row">
            <!-- Client Information -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <h5><i class="fas fa-user-circle"></i> Client Information</h5>
                    
                    {% if client.profile_picture %}
                    <div class="profile-picture text-center mb-3">
                        <img src="{{ client.profile_picture.url }}" alt="{{ client.first_name }}" 
                             class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">
                    </div>
                    {% else %}
                    <div class="profile-picture text-center mb-3">
                        <i class="fas fa-user-circle fa-5x text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <div class="client-info">
                        <div class="info-item">
                            <label>Full Name</label>
                            <div class="info-value">{{ client.first_name }} {{ client.last_name }}</div>
                        </div>
                        
                        <div class="info-item">
                            <label>Username</label>
                            <div class="info-value">{{ client.username }}</div>
                        </div>
                        
                        <div class="info-item">
                            <label>Email</label>
                            <div class="info-value">
                                <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <label>Phone</label>
                            <div class="info-value">
                                {% if client.phone_number %}
                                    <a href="tel:{{ client.phone_number }}">{{ client.phone_number }}</a>
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <label>Date of Birth</label>
                            <div class="info-value">
                                {% if client.date_of_birth %}
                                    {{ client.date_of_birth|date:"F d, Y" }}
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <label>Address</label>
                            <div class="info-value">
                                {% if client.address %}
                                    {{ client.address }}
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <label>Member Since</label>
                            <div class="info-value">{{ client.date_joined|date:"F d, Y" }}</div>
                        </div>
                        
                        <div class="info-item">
                            <label>Last Login</label>
                            <div class="info-value">
                                {% if client.last_login %}
                                    {{ client.last_login|date:"F d, Y g:i A" }}
                                {% else %}
                                    <span class="text-muted">Never</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="quick-actions mt-4">
                        <h6>Quick Actions</h6>
                        <div class="d-grid gap-2">
                            <a href="mailto:{{ client.email }}" class="admin-btn btn-primary">
                                <i class="fas fa-envelope"></i> Send Email
                            </a>
                            {% if client.phone_number %}
                            <a href="tel:{{ client.phone_number }}" class="admin-btn btn-success">
                                <i class="fas fa-phone"></i> Call Client
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bookings and Activity -->
            <div class="col-lg-8">
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number">{{ client_bookings|length }}</div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number">{{ client_reviews|length }}</div>
                            <div class="stat-label">Reviews Written</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number">
                                {% with completed_bookings=client_bookings|dictsort:"status"|slice:":5" %}
                                    {{ completed_bookings|length }}
                                {% endwith %}
                            </div>
                            <div class="stat-label">Completed Stays</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number">
                                {% with total_spent=client_bookings|length %}
                                    ${% if total_spent %}{{ total_spent|floatformat:0 }}{% else %}0{% endif %}
                                {% endwith %}
                            </div>
                            <div class="stat-label">Total Spent</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="dashboard-card mb-4">
                    <h5><i class="fas fa-calendar-check"></i> Recent Bookings</h5>
                    
                    {% if client_bookings %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Reference</th>
                                    <th>Room</th>
                                    <th>Dates</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in client_bookings|slice:":5" %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ booking.booking_reference }}</strong>
                                    </td>
                                    <td>{{ booking.room.name }}</td>
                                    <td>
                                        <div>
                                            <strong>{{ booking.check_in_date|date:"M d" }} - {{ booking.check_out_date|date:"M d, Y" }}</strong>
                                            <small class="text-muted d-block">{{ booking.get_duration_nights }} night{{ booking.get_duration_nights|pluralize }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ booking.status }}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>${{ booking.total_price }}</strong>
                                    </td>
                                    <td>
                                        <a href="{% url 'admin_interface:booking_details' booking.id %}" 
                                           class="admin-btn btn-view btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if client_bookings|length > 5 %}
                    <div class="text-center mt-3">
                        <a href="{% url 'admin_interface:bookings' %}?search={{ client.email }}" 
                           class="admin-btn btn-outline-primary">
                            <i class="fas fa-list"></i> View All Bookings ({{ client_bookings|length }})
                        </a>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No bookings yet</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Recent Reviews -->
                <div class="dashboard-card">
                    <h5><i class="fas fa-star"></i> Recent Reviews</h5>
                    
                    {% if client_reviews %}
                    <div class="reviews-list">
                        {% for review in client_reviews|slice:":3" %}
                        <div class="review-item">
                            <div class="review-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">{{ review.title }}</h6>
                                    <div class="review-rating">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.overall_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="ms-2">{{ review.overall_rating }}/5</span>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {{ review.room.name }} • {{ review.created_at|date:"M d, Y" }}
                                    {% if review.is_approved %}
                                        <span class="badge bg-success ms-2">Published</span>
                                    {% else %}
                                        <span class="badge bg-warning text-dark ms-2">Pending</span>
                                    {% endif %}
                                </small>
                            </div>
                            <div class="review-content mt-2">
                                <p class="mb-0">{{ review.comment|truncatewords:20 }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    {% if client_reviews|length > 3 %}
                    <div class="text-center mt-3">
                        <a href="{% url 'reviews:admin_reviews' %}?search={{ client.username }}" 
                           class="admin-btn btn-outline-primary">
                            <i class="fas fa-list"></i> View All Reviews ({{ client_reviews|length }})
                        </a>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-star fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No reviews yet</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.client-info .info-item {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.client-info .info-item:last-child {
    border-bottom: none;
}

.client-info .info-item label {
    font-weight: 600;
    color: var(--admin-primary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    display: block;
}

.client-info .info-value {
    font-size: 1rem;
    color: #333;
}

.stat-card {
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--admin-primary);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.review-item {
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 3px solid var(--admin-accent);
}

.review-item:last-child {
    margin-bottom: 0;
}

.review-rating {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: linear-gradient(135deg, #FFC107, #FFB300);
    color: #000;
}

.status-confirmed {
    background: linear-gradient(135deg, #28A745, #20C997);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #DC3545, #E74C3C);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, #6C757D, #495057);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, #17A2B8, #138496);
    color: white;
}

.quick-actions {
    background: rgba(144, 238, 144, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--admin-accent);
}

@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 15px;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}
</style>
{% endblock %}
