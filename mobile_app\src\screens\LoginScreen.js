import React, { useState } from 'react';
import {
  View,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../context/AuthContext';
import { theme, styles } from '../theme/theme';

export default function LoginScreen({ navigation }) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { login } = useAuth();

  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    const result = await login(username.trim(), password);
    setLoading(false);

    if (!result.success) {
      Alert.alert('Login Failed', result.error);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView contentContainerStyle={styles.centerContainer}>
        <Card style={[styles.card, { width: '100%', maxWidth: 400 }]}>
          <Card.Content>
            <View style={styles.center}>
              <Icon
                name="hotel"
                size={60}
                color={theme.colors.primary}
                style={{ marginBottom: 20 }}
              />
              <Title style={styles.title}>Welcome Back</Title>
              <Paragraph style={[styles.text, { textAlign: 'center', marginBottom: 30 }]}>
                Sign in to your Carthage Hill Guest House account
              </Paragraph>
            </View>

            <TextInput
              label="Username"
              value={username}
              onChangeText={setUsername}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="account" />}
              autoCapitalize="none"
              autoCorrect={false}
            />

            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
            />

            <Button
              mode="contained"
              onPress={handleLogin}
              style={[styles.button, { marginTop: 20 }]}
              disabled={loading}
              contentStyle={{ paddingVertical: 8 }}>
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                'Sign In'
              )}
            </Button>

            <View style={[styles.row, { marginTop: 20 }]}>
              <Text style={styles.text}>Don't have an account? </Text>
              <Button
                mode="text"
                onPress={() => navigation.navigate('Register')}
                compact>
                Sign Up
              </Button>
            </View>
          </Card.Content>
        </Card>

        <Card style={[styles.card, { marginTop: 20, width: '100%', maxWidth: 400 }]}>
          <Card.Content>
            <Title style={[styles.subtitle, { textAlign: 'center' }]}>
              Why Choose Carthage Hill?
            </Title>
            <View style={{ marginTop: 15 }}>
              <View style={[styles.row, { marginBottom: 10 }]}>
                <Icon name="wifi" size={20} color={theme.colors.primary} />
                <Text style={[styles.text, { marginLeft: 10 }]}>Free WiFi</Text>
              </View>
              <View style={[styles.row, { marginBottom: 10 }]}>
                <Icon name="pool" size={20} color={theme.colors.primary} />
                <Text style={[styles.text, { marginLeft: 10 }]}>Swimming Pool</Text>
              </View>
              <View style={[styles.row, { marginBottom: 10 }]}>
                <Icon name="restaurant" size={20} color={theme.colors.primary} />
                <Text style={[styles.text, { marginLeft: 10 }]}>Restaurant</Text>
              </View>
              <View style={styles.row}>
                <Icon name="room-service" size={20} color={theme.colors.primary} />
                <Text style={[styles.text, { marginLeft: 10 }]}>24/7 Service</Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
