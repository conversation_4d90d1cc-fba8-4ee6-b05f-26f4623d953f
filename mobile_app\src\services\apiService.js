import axios from 'axios';

// Configure base URL - change this to your Django server URL
const BASE_URL = 'http://********:8000/api'; // For Android emulator
// const BASE_URL = 'http://localhost:8000/api'; // For iOS simulator
// const BASE_URL = 'http://YOUR_IP_ADDRESS:8000/api'; // For physical device

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`Making ${config.method.toUpperCase()} request to ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log(`Response from ${response.config.url}:`, response.status);
        return response;
      },
      (error) => {
        console.error('Response error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  setAuthToken(token) {
    if (token) {
      this.api.defaults.headers.common['Authorization'] = `Token ${token}`;
    } else {
      delete this.api.defaults.headers.common['Authorization'];
    }
  }

  // Authentication
  login(username, password) {
    return this.api.post('/auth/login/', { username, password });
  }

  register(userData) {
    return this.api.post('/auth/register/', userData);
  }

  getProfile() {
    return this.api.get('/auth/profile/');
  }

  updateProfile(userData) {
    return this.api.patch('/auth/profile/', userData);
  }

  // Rooms
  getRooms(params = {}) {
    return this.api.get('/rooms/', { params });
  }

  getRoom(id) {
    return this.api.get(`/rooms/${id}/`);
  }

  checkRoomAvailability(roomId, checkIn, checkOut) {
    return this.api.get(`/rooms/${roomId}/availability/`, {
      params: {
        check_in_date: checkIn,
        check_out_date: checkOut,
      },
    });
  }

  // Bookings
  getBookings() {
    return this.api.get('/bookings/');
  }

  getBooking(id) {
    return this.api.get(`/bookings/${id}/`);
  }

  createBooking(bookingData) {
    return this.api.post('/bookings/', bookingData);
  }

  updateBooking(id, bookingData) {
    return this.api.patch(`/bookings/${id}/`, bookingData);
  }

  cancelBooking(id) {
    return this.api.delete(`/bookings/${id}/`);
  }

  // Messages
  getConversations() {
    return this.api.get('/conversations/');
  }

  getConversation(id) {
    return this.api.get(`/conversations/${id}/`);
  }

  startConversation() {
    return this.api.post('/conversations/start/');
  }

  getMessages(conversationId) {
    return this.api.get(`/conversations/${conversationId}/messages/`);
  }

  sendMessage(conversationId, messageData) {
    return this.api.post(`/conversations/${conversationId}/messages/`, messageData);
  }

  // Notifications
  getNotifications() {
    return this.api.get('/notifications/');
  }

  markNotificationRead(id) {
    return this.api.post(`/notifications/${id}/read/`);
  }

  markAllNotificationsRead() {
    return this.api.post('/notifications/mark-all-read/');
  }

  // Reviews
  getReviews(params = {}) {
    return this.api.get('/reviews/', { params });
  }

  getMyReviews() {
    return this.api.get('/reviews/my/');
  }

  createReview(reviewData) {
    return this.api.post('/reviews/create/', reviewData);
  }

  updateReview(id, reviewData) {
    return this.api.patch(`/reviews/${id}/`, reviewData);
  }
}

export const apiService = new ApiService();
