from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from datetime import timedelta
from django.db.models import Q, Count, Sum
from django.core.paginator import Paginator

from accounts.models import User
from bookings.models import Booking, Room
from reviews.models import Review
from messaging.models import Conversation, Message, Notification


class AdminRequiredMixin(UserPassesTestMixin):
    """Mixin to ensure only admin users can access views"""
    def test_func(self):
        return self.request.user.is_authenticated and self.request.user.is_admin


class AdminDashboardView(AdminRequiredMixin, ListView):
    """Enhanced admin dashboard with comprehensive statistics"""
    template_name = 'admin_interface/dashboard.html'
    context_object_name = 'dashboard_data'

    def get_queryset(self):
        return None  # We'll use get_context_data instead

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Date ranges
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # Booking statistics
        context['total_bookings'] = Booking.objects.count()
        context['pending_bookings'] = Booking.objects.filter(status='pending').count()
        context['confirmed_bookings'] = Booking.objects.filter(status='confirmed').count()
        context['cancelled_bookings'] = Booking.objects.filter(status='cancelled').count()
        context['weekly_bookings'] = Booking.objects.filter(created_at__gte=week_ago).count()

        # Revenue statistics
        confirmed_bookings = Booking.objects.filter(status='confirmed')
        context['total_revenue'] = sum(booking.total_price for booking in confirmed_bookings)
        context['monthly_revenue'] = sum(
            booking.total_price for booking in confirmed_bookings.filter(created_at__gte=month_ago)
        )

        # Room statistics
        context['total_rooms'] = Room.objects.count()
        context['available_rooms'] = Room.objects.filter(is_available=True).count()
        context['occupied_rooms'] = Booking.objects.filter(
            status='confirmed',
            check_in_date__lte=today,
            check_out_date__gte=today
        ).count()

        # Client statistics
        context['total_clients'] = User.objects.filter(user_type='client').count()
        context['new_clients'] = User.objects.filter(
            user_type='client',
            date_joined__gte=month_ago
        ).count()

        # Review statistics
        context['total_reviews'] = Review.objects.count()
        context['pending_reviews'] = Review.objects.filter(is_approved=False).count()
        context['approved_reviews'] = Review.objects.filter(is_approved=True).count()

        # Recent activity
        context['recent_bookings'] = Booking.objects.order_by('-created_at')[:5]
        context['recent_reviews'] = Review.objects.order_by('-created_at')[:5]
        context['recent_clients'] = User.objects.filter(user_type='client').order_by('-date_joined')[:5]

        # Messaging statistics
        context['unread_messages'] = Message.objects.filter(
            is_read=False,
            sender__user_type='client'
        ).count()

        return context


class AdminBookingsView(AdminRequiredMixin, ListView):
    """View for managing all bookings"""
    model = Booking
    template_name = 'admin_interface/bookings.html'
    context_object_name = 'bookings'
    paginate_by = 20

    def get_queryset(self):
        queryset = Booking.objects.select_related('user', 'room').order_by('-created_at')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(booking_reference__icontains=search) |
                Q(user__username__icontains=search) |
                Q(user__email__icontains=search) |
                Q(room__name__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_counts'] = {
            'pending': Booking.objects.filter(status='pending').count(),
            'confirmed': Booking.objects.filter(status='confirmed').count(),
            'cancelled': Booking.objects.filter(status='cancelled').count(),
            'completed': Booking.objects.filter(status='completed').count(),
        }
        return context


class PendingBookingsView(AdminRequiredMixin, ListView):
    """View for pending bookings only"""
    model = Booking
    template_name = 'admin_interface/pending_bookings.html'
    context_object_name = 'bookings'
    paginate_by = 20

    def get_queryset(self):
        return Booking.objects.filter(status='pending').select_related('user', 'room').order_by('-created_at')


class ConfirmedBookingsView(AdminRequiredMixin, ListView):
    """View for confirmed bookings only"""
    model = Booking
    template_name = 'admin_interface/confirmed_bookings.html'
    context_object_name = 'bookings'
    paginate_by = 20

    def get_queryset(self):
        return Booking.objects.filter(status='confirmed').select_related('user', 'room').order_by('-created_at')


class CancelledBookingsView(AdminRequiredMixin, ListView):
    """View for cancelled bookings only"""
    model = Booking
    template_name = 'admin_interface/cancelled_bookings.html'
    context_object_name = 'bookings'
    paginate_by = 20

    def get_queryset(self):
        return Booking.objects.filter(status='cancelled').select_related('user', 'room').order_by('-created_at')


class BookingDetailView(AdminRequiredMixin, DetailView):
    """Detailed view of a specific booking"""
    model = Booking
    template_name = 'admin_interface/booking_detail.html'
    context_object_name = 'booking'
    pk_url_kwarg = 'booking_id'


@login_required
def approve_booking(request, booking_id):
    """Approve a pending booking"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    booking = get_object_or_404(Booking, id=booking_id)

    if request.method == 'POST':
        booking.status = 'confirmed'
        booking.confirmed_by = request.user
        booking.confirmed_at = timezone.now()
        booking.save()

        # Create notification for user
        Notification.objects.create(
            user=booking.user,
            notification_type='booking_confirmed',
            title='Booking Confirmed!',
            message=f'Your booking {booking.booking_reference} has been confirmed.',
            related_booking=booking
        )

        messages.success(request, f'Booking {booking.booking_reference} approved successfully.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': 'Booking approved successfully.'})

        return redirect('admin_interface:pending_bookings')

    return render(request, 'admin_interface/approve_booking.html', {'booking': booking})


@login_required
def decline_booking(request, booking_id):
    """Decline a pending booking"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    booking = get_object_or_404(Booking, id=booking_id)

    if request.method == 'POST':
        reason = request.POST.get('reason', 'No reason provided')
        booking.status = 'cancelled'
        booking.save()

        # Create notification for user
        Notification.objects.create(
            user=booking.user,
            notification_type='booking_cancelled',
            title='Booking Declined',
            message=f'Your booking {booking.booking_reference} has been declined. Reason: {reason}',
            related_booking=booking
        )

        messages.success(request, f'Booking {booking.booking_reference} declined.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': 'Booking declined successfully.'})

        return redirect('admin_interface:pending_bookings')

    return render(request, 'admin_interface/decline_booking.html', {'booking': booking})


class AdminRoomsView(AdminRequiredMixin, ListView):
    """View for managing rooms"""
    model = Room
    template_name = 'admin_interface/rooms.html'
    context_object_name = 'rooms'
    paginate_by = 20

    def get_queryset(self):
        return Room.objects.order_by('name')


class AdminReviewsView(AdminRequiredMixin, ListView):
    """View for managing reviews"""
    model = Review
    template_name = 'admin_interface/reviews.html'
    context_object_name = 'reviews'
    paginate_by = 20

    def get_queryset(self):
        return Review.objects.select_related('user', 'room', 'booking').order_by('-created_at')


class PendingReviewsView(AdminRequiredMixin, ListView):
    """View for pending reviews only"""
    model = Review
    template_name = 'admin_interface/pending_reviews.html'
    context_object_name = 'reviews'
    paginate_by = 20

    def get_queryset(self):
        return Review.objects.filter(is_approved=False).select_related('user', 'room', 'booking').order_by('-created_at')


@login_required
def approve_review(request, review_id):
    """Approve a pending review"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    review = get_object_or_404(Review, id=review_id)
    review.is_approved = True
    review.save()

    messages.success(request, f'Review by {review.user.username} approved successfully.')

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'success': True, 'message': 'Review approved successfully.'})

    return redirect('admin_interface:pending_reviews')


@login_required
def reject_review(request, review_id):
    """Reject a pending review"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    review = get_object_or_404(Review, id=review_id)

    if request.method == 'POST':
        review.delete()
        messages.success(request, 'Review rejected and deleted.')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': 'Review rejected successfully.'})

        return redirect('admin_interface:pending_reviews')

    return render(request, 'admin_interface/reject_review.html', {'review': review})


class AdminClientsView(AdminRequiredMixin, ListView):
    """View for managing clients"""
    model = User
    template_name = 'admin_interface/clients.html'
    context_object_name = 'clients'
    paginate_by = 20

    def get_queryset(self):
        return User.objects.filter(user_type='client').order_by('-date_joined')


class ClientDetailView(AdminRequiredMixin, DetailView):
    """Detailed view of a specific client"""
    model = User
    template_name = 'admin_interface/client_detail.html'
    context_object_name = 'client'
    pk_url_kwarg = 'client_id'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        client = self.get_object()
        context['client_bookings'] = Booking.objects.filter(user=client).order_by('-created_at')
        context['client_reviews'] = Review.objects.filter(user=client).order_by('-created_at')
        return context


class AnalyticsView(AdminRequiredMixin, ListView):
    """Analytics and reporting view"""
    template_name = 'admin_interface/analytics.html'

    def get_queryset(self):
        return None

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Monthly booking trends
        monthly_bookings = []
        for i in range(12):
            month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)
            count = Booking.objects.filter(
                created_at__gte=month_start,
                created_at__lt=month_end
            ).count()
            monthly_bookings.append({
                'month': month_start.strftime('%B %Y'),
                'count': count
            })

        context['monthly_bookings'] = list(reversed(monthly_bookings))

        # Room popularity
        room_bookings = Room.objects.annotate(
            booking_count=Count('booking')
        ).order_by('-booking_count')[:10]

        context['popular_rooms'] = room_bookings

        return context


class ReportsView(AdminRequiredMixin, ListView):
    """Reports view"""
    template_name = 'admin_interface/reports.html'

    def get_queryset(self):
        return None


class AdminMessagesView(AdminRequiredMixin, ListView):
    """Admin messages view"""
    model = Conversation
    template_name = 'admin_interface/messages.html'
    context_object_name = 'conversations'
    paginate_by = 20

    def get_queryset(self):
        return Conversation.objects.order_by('-updated_at')


class AdminConversationView(AdminRequiredMixin, DetailView):
    """Admin conversation detail view"""
    model = Conversation
    template_name = 'admin_interface/conversation.html'
    context_object_name = 'conversation'
    pk_url_kwarg = 'conversation_id'


class AdminSettingsView(AdminRequiredMixin, ListView):
    """Admin settings view"""
    template_name = 'admin_interface/settings.html'

    def get_queryset(self):
        return None


# Additional utility views
class AddRoomView(AdminRequiredMixin, CreateView):
    """Add new room"""
    model = Room
    template_name = 'admin_interface/add_room.html'
    fields = ['name', 'room_type', 'capacity', 'price_per_night', 'description', 'amenities', 'room_image', 'is_available']

    def get_success_url(self):
        messages.success(self.request, 'Room added successfully.')
        return '/admin-interface/rooms/'


class EditRoomView(AdminRequiredMixin, UpdateView):
    """Edit existing room"""
    model = Room
    template_name = 'admin_interface/edit_room.html'
    fields = ['name', 'room_type', 'capacity', 'price_per_night', 'description', 'amenities', 'room_image', 'is_available']
    pk_url_kwarg = 'room_id'

    def get_success_url(self):
        messages.success(self.request, 'Room updated successfully.')
        return '/admin-interface/rooms/'


@login_required
def delete_room(request, room_id):
    """Delete a room"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    room = get_object_or_404(Room, id=room_id)

    if request.method == 'POST':
        room_name = room.name
        room.delete()
        messages.success(request, f'Room "{room_name}" deleted successfully.')
        return redirect('admin_interface:rooms')

    return render(request, 'admin_interface/delete_room.html', {'room': room})
