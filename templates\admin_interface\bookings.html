{% extends 'admin_base.html' %}

{% block title %}Admin Interface - Bookings - Carthage Hill Guest House{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% load static %}{% static 'css/admin_interface.css' %}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-calendar-check"></i> Booking Management</h1>
                    <p class="admin-subtitle">Manage all booking requests and reservations</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'admin_interface:dashboard' %}" class="admin-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Status Filter Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card stat-card text-center">
                    <div class="stat-icon text-warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">{{ status_counts.pending }}</div>
                    <div class="stat-label">Pending</div>
                    <a href="?status=pending" class="admin-btn btn-warning btn-sm mt-2">View Pending</a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card stat-card text-center">
                    <div class="stat-icon text-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-number">{{ status_counts.confirmed }}</div>
                    <div class="stat-label">Confirmed</div>
                    <a href="?status=confirmed" class="admin-btn btn-success btn-sm mt-2">View Confirmed</a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card stat-card text-center">
                    <div class="stat-icon text-danger">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-number">{{ status_counts.cancelled }}</div>
                    <div class="stat-label">Cancelled</div>
                    <a href="?status=cancelled" class="admin-btn btn-danger btn-sm mt-2">View Cancelled</a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card stat-card text-center">
                    <div class="stat-icon text-info">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <div class="stat-number">{{ status_counts.completed }}</div>
                    <div class="stat-label">Completed</div>
                    <a href="?status=completed" class="admin-btn btn-info btn-sm mt-2">View Completed</a>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h5><i class="fas fa-filter"></i> Filter & Search</h5>
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="confirmed" {% if request.GET.status == 'confirmed' %}selected{% endif %}>Confirmed</option>
                                <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>Rejected</option>
                                <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>Completed</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by reference, guest name, email, or room..." 
                                   value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button type="submit" class="admin-btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <a href="{% url 'admin_interface:bookings' %}" class="admin-btn btn-secondary">
                                    <i class="fas fa-refresh"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Bookings Table -->
        <div class="row">
            <div class="col-12">
                {% if bookings %}
                <div class="admin-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>Guest</th>
                                <th>Room</th>
                                <th>Dates</th>
                                <th>Guests</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for booking in bookings %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ booking.booking_reference }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ booking.user.first_name }} {{ booking.user.last_name }}</strong>
                                        <small class="text-muted d-block">{{ booking.user.email }}</small>
                                        <small class="text-muted d-block">{{ booking.user.phone_number|default:"No phone" }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ booking.room.name }}</strong>
                                        <small class="text-muted d-block">{{ booking.room.get_room_type_display }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ booking.check_in_date|date:"M d" }} - {{ booking.check_out_date|date:"M d, Y" }}</strong>
                                        <small class="text-muted d-block">{{ booking.get_duration_nights }} night{{ booking.get_duration_nights|pluralize }}</small>
                                    </div>
                                </td>
                                <td>{{ booking.guests_count }}</td>
                                <td>
                                    <strong>${{ booking.total_price }}</strong>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ booking.status }}">
                                        {{ booking.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ booking.created_at|date:"M d, Y" }}</strong>
                                        <small class="text-muted d-block">{{ booking.created_at|time:"g:i A" }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{% url 'admin_interface:booking_details' booking.id %}" 
                                           class="admin-btn btn-view btn-sm" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if booking.status == 'pending' %}
                                        <form method="post" action="{% url 'admin_interface:approve_booking' booking.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="admin-btn btn-approve btn-sm" 
                                                    title="Approve Booking"
                                                    onclick="return confirm('Approve booking {{ booking.booking_reference }}?')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        <form method="post" action="{% url 'admin_interface:decline_booking' booking.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="admin-btn btn-decline btn-sm" 
                                                    title="Decline Booking"
                                                    onclick="return confirm('Decline booking {{ booking.booking_reference }}?')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Bookings pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="dashboard-card text-center py-5">
                    <i class="fas fa-calendar-times fa-5x text-muted mb-4"></i>
                    <h4>No bookings found</h4>
                    <p class="text-muted">
                        {% if request.GET.status or request.GET.search %}
                            No bookings match your current filters.
                        {% else %}
                            No booking requests have been submitted yet.
                        {% endif %}
                    </p>
                    {% if request.GET.status or request.GET.search %}
                    <a href="{% url 'admin_interface:bookings' %}" class="admin-btn btn-primary">
                        <i class="fas fa-refresh"></i> Clear Filters
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.action-buttons .admin-btn {
    min-width: 35px;
    padding: 8px 12px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: linear-gradient(135deg, #FFC107, #FFB300);
    color: #000;
}

.status-confirmed {
    background: linear-gradient(135deg, #28A745, #20C997);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #DC3545, #E74C3C);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, #6C757D, #495057);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, #17A2B8, #138496);
    color: white;
}

@media (max-width: 768px) {
    .admin-table {
        font-size: 0.875rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
    
    .action-buttons .admin-btn {
        width: 100%;
        min-width: auto;
    }
}
</style>
{% endblock %}
