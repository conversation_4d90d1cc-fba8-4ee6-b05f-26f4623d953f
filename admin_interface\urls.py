from django.urls import path
from . import views

app_name = 'admin_interface'

urlpatterns = [
    # Main admin dashboard
    path('', views.AdminDashboardView.as_view(), name='dashboard'),
    
    # Booking management
    path('bookings/', views.AdminBookingsView.as_view(), name='bookings'),
    path('bookings/pending/', views.PendingBookingsView.as_view(), name='pending_bookings'),
    path('bookings/confirmed/', views.ConfirmedBookingsView.as_view(), name='confirmed_bookings'),
    path('bookings/cancelled/', views.CancelledBookingsView.as_view(), name='cancelled_bookings'),
    path('bookings/<int:booking_id>/approve/', views.approve_booking, name='approve_booking'),
    path('bookings/<int:booking_id>/decline/', views.decline_booking, name='decline_booking'),
    path('bookings/<int:booking_id>/details/', views.BookingDetailView.as_view(), name='booking_details'),
    
    # Room management
    path('rooms/', views.AdminRoomsView.as_view(), name='rooms'),
    path('rooms/add/', views.AddRoomView.as_view(), name='add_room'),
    path('rooms/<int:room_id>/edit/', views.EditRoomView.as_view(), name='edit_room'),
    path('rooms/<int:room_id>/delete/', views.delete_room, name='delete_room'),
    
    # Review management
    path('reviews/', views.AdminReviewsView.as_view(), name='reviews'),
    path('reviews/pending/', views.PendingReviewsView.as_view(), name='pending_reviews'),
    path('reviews/<int:review_id>/approve/', views.approve_review, name='approve_review'),
    path('reviews/<int:review_id>/reject/', views.reject_review, name='reject_review'),
    
    # Client management
    path('clients/', views.AdminClientsView.as_view(), name='clients'),
    path('clients/<int:client_id>/details/', views.ClientDetailView.as_view(), name='client_details'),
    
    # Analytics and reports
    path('analytics/', views.AnalyticsView.as_view(), name='analytics'),
    path('reports/', views.ReportsView.as_view(), name='reports'),
    
    # Messaging
    path('messages/', views.AdminMessagesView.as_view(), name='messages'),
    path('messages/<int:conversation_id>/', views.AdminConversationView.as_view(), name='conversation'),
    
    # Settings
    path('settings/', views.AdminSettingsView.as_view(), name='settings'),
]
