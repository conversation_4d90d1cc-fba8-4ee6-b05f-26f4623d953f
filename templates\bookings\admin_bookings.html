{% extends 'base.html' %}

{% block title %}Admin - Manage Bookings - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="admin-bookings-container">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h2><i class="fas fa-calendar-check"></i> Manage Bookings</h2>
                <p class="text-muted">Review and manage all booking requests</p>
            </div>
        </div>
    </div>

    <!-- Filter and Search -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Filter by Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="confirmed" {% if status_filter == 'confirmed' %}selected{% endif %}>Confirmed</option>
                                <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                                <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by reference, user, or room..." value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5>Quick Stats</h5>
                    <div class="row">
                        <div class="col-6">
                            <div class="stat-item">
                                <strong>{{ bookings|length }}</strong>
                                <small class="text-muted d-block">Total</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <strong>{{ bookings|length }}</strong>
                                <small class="text-muted d-block">Filtered</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bookings Table -->
    <div class="row">
        <div class="col-12">
            {% if bookings %}
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> Booking Requests</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Reference</th>
                                    <th>Guest</th>
                                    <th>Room</th>
                                    <th>Check-in</th>
                                    <th>Check-out</th>
                                    <th>Guests</th>
                                    <th>Total Price</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in bookings %}
                                <tr>
                                    <td>
                                        <strong>{{ booking.booking_reference }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ booking.user.first_name }} {{ booking.user.last_name }}</strong>
                                            <small class="text-muted d-block">{{ booking.user.email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ booking.room.name }}</strong>
                                        <small class="text-muted d-block">{{ booking.room.get_room_type_display }}</small>
                                    </td>
                                    <td>{{ booking.check_in_date|date:"M d, Y" }}</td>
                                    <td>{{ booking.check_out_date|date:"M d, Y" }}</td>
                                    <td>{{ booking.guests_count }}</td>
                                    <td>${{ booking.total_price }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if booking.status == 'pending' %}bg-warning text-dark
                                            {% elif booking.status == 'confirmed' %}bg-success
                                            {% elif booking.status == 'rejected' %}bg-danger
                                            {% elif booking.status == 'cancelled' %}bg-secondary
                                            {% elif booking.status == 'completed' %}bg-info
                                            {% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ booking.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'bookings:booking_detail' booking.id %}" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if booking.status == 'pending' %}
                                            <form method="post" action="{% url 'bookings:approve_booking' booking.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-success" 
                                                        title="Approve Booking"
                                                        onclick="return confirm('Are you sure you want to approve this booking?')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <form method="post" action="{% url 'bookings:reject_booking' booking.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                        title="Reject Booking"
                                                        onclick="return confirm('Are you sure you want to reject this booking?')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Bookings pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calendar-times fa-5x text-muted mb-4"></i>
                    <h4>No bookings found</h4>
                    <p class="text-muted">
                        {% if status_filter or request.GET.search %}
                            No bookings match your current filters. Try adjusting your search criteria.
                        {% else %}
                            No booking requests have been submitted yet.
                        {% endif %}
                    </p>
                    {% if status_filter or request.GET.search %}
                    <a href="{% url 'bookings:admin_bookings' %}" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> Clear Filters
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.admin-bookings-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.8), rgba(144, 238, 144, 0.1));
    min-height: 80vh;
    padding: 20px;
    border-radius: 15px;
}

.page-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-item {
    padding: 10px 0;
}

.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.card-header {
    background: linear-gradient(135deg, #90EE90, #32CD32);
    border-radius: 15px 15px 0 0 !important;
    color: #228B22;
    font-weight: bold;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    border-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(144, 238, 144, 0.1);
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
</style>
{% endblock %}
